@echo off
echo 🧪 简单测试脚本
echo.

REM 检查Go是否可用
echo 🔍 检查Go安装...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go未找到，尝试常见安装路径...
    
    if exist "C:\Go\bin\go.exe" (
        echo ✅ 找到Go: C:\Go\bin\go.exe
        set GO_CMD=C:\Go\bin\go.exe
    ) else if exist "C:\Program Files\Go\bin\go.exe" (
        echo ✅ 找到Go: C:\Program Files\Go\bin\go.exe
        set GO_CMD="C:\Program Files\Go\bin\go.exe"
    ) else (
        echo ❌ 未找到Go安装，请手动安装Go或设置PATH
        pause
        exit /b 1
    )
) else (
    echo ✅ Go已在PATH中
    set GO_CMD=go
)

echo.
echo 🚀 运行调试测试...
echo.

REM 切换到正确目录
cd /d "%~dp0"

REM 检查环境变量
if "%OPENROUTER_API_KEY%"=="" (
    echo ⚠️  警告: OPENROUTER_API_KEY 环境变量未设置
    echo    请设置API密钥: set OPENROUTER_API_KEY=your_key_here
    echo.
)

REM 运行测试
%GO_CMD% run debug_test.go

echo.
echo 🎯 测试完成
pause
