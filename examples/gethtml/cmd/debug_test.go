package main_test

import (
	"context"
	"fmt"
	"os"
	"time"

	gethtml "github.com/ynl/greensoulai/examples/gethtml"
)

func main() {
	fmt.Println("🧪 调试HTML获取工具...")

	// 设置环境变量（如果没有设置的话）
	if os.Getenv("OPENROUTER_API_KEY") == "" {
		fmt.Println("⚠️  OPENROUTER_API_KEY 环境变量未设置，程序可能会失败")
	}

	// 创建测试配置
	config := gethtml.GetHTMLConfig{
		URL:      "https://httpbin.org/html", // 使用简单的测试URL
		SaveDir:  "./debug_output",
		Filename: "debug_test.html",
	}

	fmt.Printf("📋 测试配置:\n")
	fmt.Printf("   URL: %s\n", config.URL)
	fmt.Printf("   保存目录: %s\n", config.SaveDir)
	fmt.Printf("   文件名: %s\n", config.Filename)
	fmt.Println()

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	// 执行任务
	fmt.Printf("🚀 开始执行HTML获取任务...\n")
	startTime := time.Now()

	output, err := gethtml.RunGetHTML(ctx, config)
	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("❌ 任务执行失败: %v\n", err)
		os.Exit(1)
	}

	// 显示详细结果
	fmt.Printf("\n📊 任务执行结果:\n")
	fmt.Printf("   执行时间: %v\n", duration)
	fmt.Printf("   Agent: %s\n", output.Agent)
	fmt.Printf("   使用的工具: %v\n", output.ToolsUsed)
	fmt.Printf("   Token使用量: %d\n", output.TokensUsed)
	fmt.Printf("   成本: %.6f\n", output.Cost)
	fmt.Printf("   模型: %s\n", output.Model)
	fmt.Printf("   任务ID: %s\n", output.Task)

	if output.Raw != "" {
		fmt.Printf("   LLM原始响应: %s\n", output.Raw)
	}

	// 检查文件是否存在
	filePath := fmt.Sprintf("%s/%s", config.SaveDir, config.Filename)
	if stat, err := os.Stat(filePath); err == nil {
		fmt.Printf("\n✅ 文件保存成功!\n")
		fmt.Printf("   文件路径: %s\n", filePath)
		fmt.Printf("   文件大小: %d 字节\n", stat.Size())
		fmt.Printf("   修改时间: %v\n", stat.ModTime())
	} else {
		fmt.Printf("\n❌ 文件未找到: %s\n", filePath)
		fmt.Printf("   错误: %v\n", err)

		// 尝试列出目录内容
		if entries, err := os.ReadDir(config.SaveDir); err == nil {
			fmt.Printf("   目录 %s 的内容:\n", config.SaveDir)
			for _, entry := range entries {
				fmt.Printf("     - %s\n", entry.Name())
			}
		} else {
			fmt.Printf("   无法读取目录 %s: %v\n", config.SaveDir, err)
		}
	}

	fmt.Println("\n🎯 调试测试完成!")
}
