package main

import (
	"context"
	"flag"
	"fmt"
	_ "log"
	"os"
	"path/filepath"
	"time"

	gethtml "github.com/ynl/greensoulai/examples/gethtml"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
)

func main() {
	// 定义命令行参数
	var (
		url      = flag.String("url", "", "要获取HTML的URL地址 (必需)")
		saveDir  = flag.String("dir", "./html_output", "保存HTML文件的目录")
		filename = flag.String("file", "", "保存的文件名 (可选，默认自动生成)")
		timeout  = flag.Duration("timeout", 60*time.Second, "请求超时时间")
		verbose  = flag.Bool("verbose", false, "启用详细日志输出")
		help     = flag.Bool("help", false, "显示帮助信息")
	)

	flag.Parse()

	// 显示帮助信息
	if *help {
		showHelp()
		return
	}

	// 验证必需参数
	if *url == "" {
		fmt.Fprintf(os.Stderr, "没有url，默认为https://www.github.com/\n\n")
		url = &[]string{"https://www.github.com/"}[0]
	}

	// 创建日志器
	baseLogger := &silentLogger{}

	// 创建事件总线
	eventBus := events.NewEventBus(baseLogger)

	// 生成文件名（如果未指定）
	if *filename == "" {
		*filename = gethtml.GenerateFilename(*url)
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), *timeout)
	defer cancel()

	// 显示开始信息
	fmt.Printf("🌐 开始获取HTML内容...\n")
	fmt.Printf("URL: %s\n", *url)
	fmt.Printf("保存目录: %s\n", *saveDir)
	fmt.Printf("文件名: %s\n", *filename)
	fmt.Printf("超时时间: %v\n", *timeout)
	fmt.Println()

	// 显示Agent工作模式说明
	fmt.Printf("🤖 AI Agent工作模式:\n")
	fmt.Printf("   Agent将使用以下工具完成任务:\n")
	fmt.Printf("   1. fetch_and_save_html - 从URL获取HTML内容并保存到本地文件\n")
	fmt.Printf("   Agent会智能地调用工具并执行任务\n")
	fmt.Println()

	// 配置获取任务
	config := gethtml.GetHTMLConfig{
		URL:      *url,
		SaveDir:  *saveDir,
		Filename: *filename,
	}

	// 执行HTML获取任务（通过Agent调用工具）
	fmt.Printf("🔄 Agent正在分析任务并调用工具...\n")
	startTime := time.Now()
	output, err := gethtml.RunGetHTMLWithEventBus(ctx, config, eventBus)
	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("❌ Agent执行失败: %v\n", err)
		os.Exit(1)
	}

	// 显示成功信息
	fmt.Printf("✅ Agent成功完成HTML获取任务！\n")
	fmt.Printf("执行时间: %v\n", duration)
	fmt.Printf("保存路径: %s\n", filepath.Join(*saveDir, *filename))

	// 显示Agent任务详情
	if output.Task != "" {
		fmt.Printf("任务ID: %s\n", output.Task)
	}
	if output.ToolsUsed != nil {
		fmt.Printf("任务是否使用工具: %s\n", output.ToolsUsed)
	}
	fmt.Println()

	// 显示详细输出
	if *verbose {
		fmt.Println("--- Agent详细输出 ---")
		if output.Raw != "" {
			fmt.Println(output.Raw)
		}
		if output.Summary != "" {
			fmt.Printf("Agent总结: %s\n", output.Summary)
		}
		if output.ExecutionTime > 0 {
			fmt.Printf("Agent执行时间: %v\n", output.ExecutionTime)
		}
	}

	fmt.Println("🎉 AI Agent任务完成！")
}

func showHelp() {
	fmt.Println("GreenSoulAI GetHTML - 智能HTML获取工具")
	fmt.Println()
	fmt.Println("这是一个基于AI Agent的HTML内容获取工具，能够从指定URL获取HTML内容并保存到本地文件。")
	fmt.Println()
	showUsage()
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 获取网页并保存到默认目录")
	fmt.Println("  ./gethtml -url https://example.com")
	fmt.Println()
	fmt.Println("  # 指定保存目录和文件名")
	fmt.Println("  ./gethtml -url https://example.com -dir ./my_html -file example.html")
	fmt.Println()
	fmt.Println("  # 启用详细日志")
	fmt.Println("  ./gethtml -url https://example.com -verbose")
	fmt.Println()
	fmt.Println("环境变量:")
	fmt.Println("  OPENROUTER_API_KEY  OpenRouter API密钥 (必需)")
	fmt.Println()
}

func showUsage() {
	fmt.Println("用法:")
	fmt.Println("  gethtml -url <URL> [选项]")
	fmt.Println()
	fmt.Println("选项:")
	flag.PrintDefaults()
}

type silentLogger struct{}

func (l *silentLogger) Debug(msg string, fields ...logger.Field)  {}
func (l *silentLogger) Info(msg string, fields ...logger.Field)   {}
func (l *silentLogger) Warn(msg string, fields ...logger.Field)   {}
func (l *silentLogger) Error(msg string, fields ...logger.Field)  {}
func (l *silentLogger) Fatal(msg string, fields ...logger.Field)  {}
func (l *silentLogger) With(fields ...logger.Field) logger.Logger { return l }
