package main

import (
	"context"
	"fmt"
	"os"
	"time"

	gethtml "github.com/ynl/greensoulai/examples/gethtml"
	"github.com/ynl/greensoulai/pkg/logger"
)

// 简单的日志器实现
type simpleLogger1 struct{}

func (l *simpleLogger1) Debug(msg string, fields ...logger.Field) { fmt.Printf("[DEBUG] %s\n", msg) }
func (l *simpleLogger1) Info(msg string, fields ...logger.Field) {
	fmt.Printf("[INFO] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger1) Warn(msg string, fields ...logger.Field)   { fmt.Printf("[WARN] %s\n", msg) }
func (l *simpleLogger1) Error(msg string, fields ...logger.Field)  { fmt.Printf("[ERROR] %s\n", msg) }
func (l *simpleLogger1) Fatal(msg string, fields ...logger.Field)  { fmt.Printf("[FATAL] %s\n", msg) }
func (l *simpleLogger1) With(fields ...logger.Field) logger.Logger { return l }

func main() {
	fmt.Println("🧪 测试SaveHTML方法...")

	// 创建HTTPFetcher实例
	fetcher := gethtml.NewHTTPFetcher("./test_save_output", &simpleLogger{})

	// 测试HTML内容
	testHTML := `<!DOCTYPE html>
<html>
<head>
    <title>测试页面</title>
</head>
<body>
    <h1>这是一个测试页面</h1>
    <p>当前时间: ` + time.Now().Format("2006-01-02 15:04:05") + `</p>
</body>
</html>`

	// 测试文件名
	filename := "test_savehtml_" + time.Now().Format("20060102_150405") + ".html"

	fmt.Printf("📝 测试内容长度: %d 字节\n", len(testHTML))
	fmt.Printf("📁 保存目录: ./test_save_output\n")
	fmt.Printf("📄 文件名: %s\n", filename)
	fmt.Println()

	// 创建上下文
	ctx := context.Background()

	// 调用SaveHTML方法
	fmt.Println("🚀 开始保存文件...")
	err := fetcher.SaveHTML(ctx, testHTML, filename)

	if err != nil {
		fmt.Printf("❌ 保存失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ 保存成功!")

	// 验证文件是否存在
	fullPath := fmt.Sprintf("./test_save_output/%s", filename)
	if stat, err := os.Stat(fullPath); err == nil {
		fmt.Printf("📊 文件信息:\n")
		fmt.Printf("   路径: %s\n", fullPath)
		fmt.Printf("   大小: %d 字节\n", stat.Size())
		fmt.Printf("   修改时间: %v\n", stat.ModTime())

		// 读取文件内容验证
		if content, err := os.ReadFile(fullPath); err == nil {
			fmt.Printf("   内容长度: %d 字节\n", len(content))
			if len(content) == len(testHTML) {
				fmt.Println("✅ 文件内容长度匹配!")
			} else {
				fmt.Printf("⚠️  文件内容长度不匹配: 期望 %d, 实际 %d\n", len(testHTML), len(content))
			}
		} else {
			fmt.Printf("⚠️  无法读取文件内容: %v\n", err)
		}
	} else {
		fmt.Printf("❌ 文件不存在: %s\n", fullPath)
		fmt.Printf("   错误: %v\n", err)
	}

	fmt.Println("\n🎯 SaveHTML测试完成!")
}
