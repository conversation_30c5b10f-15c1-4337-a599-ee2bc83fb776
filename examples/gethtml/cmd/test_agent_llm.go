package main

import (
	"context"
	"fmt"
	"os"
	_ "time"

	gethtml "github.com/ynl/greensoulai/examples/gethtml"
	"github.com/ynl/greensoulai/internal/agent"
	"github.com/ynl/greensoulai/internal/llm"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
)

// 简单的日志器实现
type simpleLogger struct{}

func (l *simpleLogger) Debug(msg string, fields ...logger.Field) {
	fmt.Printf("[DEBUG] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger) Info(msg string, fields ...logger.Field) {
	fmt.Printf("[INFO] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger) Warn(msg string, fields ...logger.Field) {
	fmt.Printf("[WARN] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger) Error(msg string, fields ...logger.Field) {
	fmt.Printf("[ERROR] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger) Fatal(msg string, fields ...logger.Field) {
	fmt.Printf("[FATAL] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger) With(fields ...logger.Field) logger.Logger { return l }

// MockLLM 模拟LLM，返回工具调用
type MockLLM struct {
	shouldCallTool bool
}

func (m *MockLLM) SupportsFunctionCalling() bool {
	//TODO implement me
	panic("implement me")
}

func (m *MockLLM) GetContextWindowSize() int {
	//TODO implement me
	panic("implement me")
}

func (m *MockLLM) SetEventBus(eventBus events.EventBus) {
	//TODO implement me
	panic("implement me")
}

func (m *MockLLM) Close() error {
	//TODO implement me
	panic("implement me")
}

func (m *MockLLM) Call(ctx context.Context, messages []llm.Message, options *llm.CallOptions) (*llm.Response, error) {
	fmt.Printf("🤖 [MockLLM] 收到调用请求\n")
	fmt.Printf("   消息数量: %d\n", len(messages))

	if options != nil && len(options.Tools) > 0 {
		fmt.Printf("   可用工具数量: %d\n", len(options.Tools))
		for i, tool := range options.Tools {
			fmt.Printf("     工具 %d: %s - %s\n", i+1, tool.Function.Name, tool.Function.Description)
		}
	}

	if m.shouldCallTool && options != nil && len(options.Tools) > 0 {
		// 返回工具调用响应
		fmt.Printf("✅ [MockLLM] 返回工具调用响应\n")
		return &llm.Response{
			Content: "我需要调用工具来获取HTML内容",
			ToolCalls: []llm.ToolCall{
				{
					ID:   "call_123",
					Type: "function",
					Function: llm.ToolCallFunction{
						Name:      "fetch_and_save_html",
						Arguments: `{"url": "https://httpbin.org/html", "filename": "mock_test.html"}`,
					},
				},
			},
			Usage: llm.Usage{
				PromptTokens:     100,
				CompletionTokens: 50,
				TotalTokens:      150,
				Cost:             0.001,
			},
			Model:        "mock-model",
			FinishReason: "tool_calls",
		}, nil
	} else {
		// 返回普通文本响应
		fmt.Printf("📝 [MockLLM] 返回文本响应（不调用工具）\n")
		return &llm.Response{
			Content: "我已经完成了HTML获取任务",
			Usage: llm.Usage{
				PromptTokens:     100,
				CompletionTokens: 20,
				TotalTokens:      120,
				Cost:             0.0008,
			},
			Model:        "mock-model",
			FinishReason: "stop",
		}, nil
	}
}

func (m *MockLLM) CallStream(ctx context.Context, messages []llm.Message, options *llm.CallOptions) (<-chan llm.StreamResponse, error) {
	return nil, fmt.Errorf("stream not implemented")
}

func (m *MockLLM) GetModel() string {
	return "mock-model"
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func main() {
	fmt.Println("🧪 测试Agent和LLM交互...")
	fmt.Println()

	// 创建HTTPFetcher实例
	fetcher := gethtml.NewHTTPFetcher("./test_agent_output", &simpleLogger{})

	// 创建工具
	tool := gethtml.NewHTMLFetchAndSaveTool(fetcher)

	// 测试1: 使用不调用工具的MockLLM
	fmt.Println("📋 测试1: LLM不调用工具")

	mockLLM1 := &MockLLM{shouldCallTool: false}
	testAgent(mockLLM1, tool, "test1")

	fmt.Println()

	// 测试2: 使用调用工具的MockLLM
	fmt.Println("📋 测试2: LLM调用工具")

	mockLLM2 := &MockLLM{shouldCallTool: true}
	testAgent(mockLLM2, tool, "test2")

	fmt.Println()
	fmt.Println("🎯 Agent和LLM交互测试完成!")

	// 检查生成的文件
	fmt.Println()
	fmt.Println("📁 检查生成的文件:")
	if entries, err := os.ReadDir("./test_agent_output"); err == nil {
		for _, entry := range entries {
			fmt.Printf("   - %s\n", entry.Name())
		}
	} else {
		fmt.Printf("   目录不存在或为空: %v\n", err)
	}
}

func testAgent(llmProvider llm.LLM, tool agent.Tool, testName string) {
	// 创建Agent
	htmlAgent, err := agent.NewBaseAgent(agent.AgentConfig{
		Role:      "HTMLFetcher",
		Goal:      "从指定URL获取HTML内容并保存到本地文件",
		Backstory: "你是一个专业的网页内容获取助手。你的任务是使用fetch_and_save_html工具来获取HTML内容并保存文件。",
		LLM:       llmProvider,
	})

	if err != nil {
		fmt.Printf("❌ 创建Agent失败: %v\n", err)
		return
	}

	// 初始化Agent
	if err := htmlAgent.Initialize(); err != nil {
		fmt.Printf("❌ 初始化Agent失败: %v\n", err)
		return
	}

	// 创建任务
	task := agent.NewTaskWithOptions(
		fmt.Sprintf("请使用fetch_and_save_html工具从URL 'https://httpbin.org/html' 获取HTML内容并保存到文件 '%s.html'。", testName),
		"成功获取HTML内容并保存到指定文件，返回操作结果和文件信息。",
		agent.WithAssignedAgent(htmlAgent),
		agent.WithTools(tool),
	)

	fmt.Printf("🎯 执行任务: %s\n", task.GetDescription())

	// 执行任务
	ctx := context.Background()
	output, err := htmlAgent.Execute(ctx, task)

	if err != nil {
		fmt.Printf("❌ 任务执行失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 任务执行成功!\n")
	fmt.Printf("   Agent: %s\n", output.Agent)
	fmt.Printf("   使用的工具: %v\n", output.ToolsUsed)
	fmt.Printf("   Token使用量: %d\n", output.TokensUsed)
	fmt.Printf("   LLM响应: %s\n", output.Raw)
}
