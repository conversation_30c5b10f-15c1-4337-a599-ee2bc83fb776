package main

import (
	"context"
	"fmt"
	"os"
	"time"

	gethtml "github.com/ynl/greensoulai/examples/gethtml"
)

func main() {
	fmt.Println("🧪 调试HTML获取工具...")

	// 设置环境变量（如果没有设置的话）
	if os.Getenv("OPENROUTER_API_KEY") == "" {
		fmt.Println("⚠️  OPENROUTER_API_KEY 环境变量未设置，程序可能会失败")
		return
	}

	// 创建测试配置
	config := gethtml.GetHTMLConfig{
		URL:      "https://httpbin.org/html", // 使用简单的测试URL
		SaveDir:  "./debug_output",
		Filename: "debug_test.html",
	}

	fmt.Printf("📋 测试配置:\n")
	fmt.Printf("   URL: %s\n", config.URL)
	fmt.Printf("   保存目录: %s\n", config.SaveDir)
	fmt.Printf("   文件名: %s\n", config.Filename)
	fmt.Println()

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second) // 5分钟超时
	defer cancel()

	// 执行任务
	fmt.Printf("🚀 开始执行HTML获取任务...\n")
	startTime := time.Now()

	output, err := gethtml.RunGetHTML(ctx, config)
	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("❌ 任务执行失败: %v\n", err)
		fmt.Printf("   执行时间: %v\n", duration)
		os.Exit(1)
	}

	// 显示详细结果
	fmt.Printf("\n📊 任务执行结果:\n")
	fmt.Printf("   执行时间: %v\n", duration)
	fmt.Printf("   任务输出: %s\n", output.Model)

	// 检查输出文件是否存在
	outputPath := fmt.Sprintf("%s/%s", config.SaveDir, config.Filename)
	if _, err := os.Stat(outputPath); err == nil {
		fmt.Printf("✅ 文件已成功保存到: %s\n", outputPath)

		// 显示文件大小
		if info, err := os.Stat(outputPath); err == nil {
			fmt.Printf("   文件大小: %d 字节\n", info.Size())
		}
	} else {
		fmt.Printf("⚠️  输出文件不存在: %s\n", outputPath)
	}

	fmt.Printf("\n🎉 调试完成!\n")
}
