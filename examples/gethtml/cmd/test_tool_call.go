package main

import (
	"context"
	"fmt"
	"os"
	"time"

	gethtml "github.com/ynl/greensoulai/examples/gethtml"
	"github.com/ynl/greensoulai/internal/agent"
	"github.com/ynl/greensoulai/internal/llm"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
)

// 简单的日志器实现
type simpleLogger2 struct{}

func (l *simpleLogger2) Debug(msg string, fields ...logger.Field) {
	fmt.Printf("[DEBUG] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger2) Info(msg string, fields ...logger.Field) {
	fmt.Printf("[INFO] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger2) Warn(msg string, fields ...logger.Field) {
	fmt.Printf("[WARN] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger2) Error(msg string, fields ...logger.Field) {
	fmt.Printf("[ERROR] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger2) Fatal(msg string, fields ...logger.Field) {
	fmt.Printf("[FATAL] %s", msg)
	for _, field := range fields {
		fmt.Printf(" %s=%v", field.Key, field.Value)
	}
	fmt.Println()
}
func (l *simpleLogger2) With(fields ...logger.Field) logger.Logger { return l }

func main() {
	fmt.Println("🧪 测试工具调用...")
	fmt.Println()

	// 创建HTTPFetcher实例
	fetcher := gethtml.NewHTTPFetcher("./test_tool_output", &simpleLogger2{})

	// 创建工具
	tool := gethtml.NewHTMLFetchAndSaveTool(fetcher)

	fmt.Printf("🔧 工具信息:\n")
	fmt.Printf("   名称: %s\n", tool.GetName())
	fmt.Printf("   描述: %s\n", tool.GetDescription())
	fmt.Printf("   Schema: %+v\n", tool.GetSchema())
	fmt.Println()

	// 测试1: 直接调用工具
	fmt.Println("📋 测试1: 直接调用工具")

	ctx := context.Background()
	args := map[string]interface{}{
		"url":      "https://httpbin.org/html",
		"filename": "direct_test.html",
	}

	fmt.Printf("🚀 直接调用工具，参数: %+v\n", args)

	result, err := tool.Execute(ctx, args)
	if err != nil {
		fmt.Printf("❌ 直接调用失败: %v\n", err)
	} else {
		fmt.Printf("✅ 直接调用成功: %+v\n", result)
	}
	fmt.Println()

	// 测试2: 通过Agent调用工具（模拟LLM响应）
	fmt.Println("📋 测试2: 模拟Agent工具调用")

	// 创建一个简单的任务
	task := agent.NewTaskWithOptions(
		"测试工具调用",
		"成功调用工具",
		agent.WithTools(tool),
	)

	fmt.Printf("🎯 任务信息:\n")
	fmt.Printf("   ID: %s\n", task.GetID())
	fmt.Printf("   描述: %s\n", task.GetDescription())
	fmt.Printf("   工具数量: %d\n", len(task.GetTools()))
	fmt.Println()

	// 检查工具是否正确添加到任务
	tools := task.GetTools()
	if len(tools) > 0 {
		fmt.Printf("✅ 任务包含工具: %s\n", tools[0].GetName())
	} else {
		fmt.Printf("❌ 任务不包含任何工具\n")
	}
	fmt.Println()

	// 测试3: 检查工具执行上下文
	fmt.Println("📋 测试3: 工具执行上下文")

	// 创建一个模拟的Agent（不需要LLM）
	mockAgent := &MockAgent{
		tools: []agent.Tool{tool},
	}

	// 创建工具执行上下文
	toolCtx := agent.NewToolExecutionContext(mockAgent, task)

	fmt.Printf("🔍 工具执行上下文:\n")
	fmt.Printf("   有工具: %t\n", toolCtx.HasTools())
	fmt.Printf("   工具数量: %d\n", len(toolCtx.Tools))
	fmt.Printf("   工具名称: %s\n", toolCtx.GetToolNames())
	fmt.Println()

	if toolCtx.HasTools() {
		fmt.Println("🚀 通过工具执行上下文调用工具...")
		result, err := toolCtx.ExecuteTool(ctx, "fetch_and_save_html", args)
		if err != nil {
			fmt.Printf("❌ 上下文调用失败: %v\n", err)
		} else {
			fmt.Printf("✅ 上下文调用成功: %+v\n", result)
		}
	}

	fmt.Println()
	fmt.Println("🎯 工具调用测试完成!")

	// 检查生成的文件
	fmt.Println()
	fmt.Println("📁 检查生成的文件:")
	if entries, err := os.ReadDir("./test_tool_output"); err == nil {
		for _, entry := range entries {
			fmt.Printf("   - %s\n", entry.Name())
		}
	} else {
		fmt.Printf("   目录不存在或为空: %v\n", err)
	}
}

// MockAgent 模拟Agent实现
type MockAgent struct {
	tools []agent.Tool
}

func (m *MockAgent) ExecuteAsync(ctx context.Context, task agent.Task) (<-chan agent.TaskResult, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) SetLLM(llm llm.LLM) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetLLM() llm.LLM {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) SetMemory(memory agent.Memory) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetMemory() agent.Memory {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) SetKnowledgeSources(sources []agent.KnowledgeSource) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetKnowledgeSources() []agent.KnowledgeSource {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) SetExecutionConfig(config agent.ExecutionConfig) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetExecutionConfig() agent.ExecutionConfig {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) SetHumanInputHandler(handler agent.HumanInputHandler) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetHumanInputHandler() agent.HumanInputHandler {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) SetEventBus(eventBus events.EventBus) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetEventBus() events.EventBus {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) SetLogger(logger logger.Logger) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetLogger() logger.Logger {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) Close() error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetExecutionStats() agent.ExecutionStats {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) ResetStats() error {
	//TODO implement me
	panic("implement me")
}

func (m *MockAgent) GetID() string          { return "mock-agent" }
func (m *MockAgent) GetRole() string        { return "MockAgent" }
func (m *MockAgent) GetGoal() string        { return "Test tools" }
func (m *MockAgent) GetBackstory() string   { return "Mock agent for testing" }
func (m *MockAgent) GetTools() []agent.Tool { return m.tools }
func (m *MockAgent) AddTool(tool agent.Tool) error {
	m.tools = append(m.tools, tool)
	return nil
}
func (m *MockAgent) RemoveTool(name string) error { return nil }
func (m *MockAgent) Execute(ctx context.Context, task agent.Task) (*agent.TaskOutput, error) {
	return nil, fmt.Errorf("not implemented")
}
func (m *MockAgent) ExecuteWithTimeout(ctx context.Context, task agent.Task, timeout time.Duration) (*agent.TaskOutput, error) {
	return nil, fmt.Errorf("not implemented")
}
func (m *MockAgent) Initialize() error              { return nil }
func (m *MockAgent) IsInitialized() bool            { return true }
func (m *MockAgent) GetStats() agent.ExecutionStats { return agent.ExecutionStats{} }
func (m *MockAgent) Clone() agent.Agent             { return m }
