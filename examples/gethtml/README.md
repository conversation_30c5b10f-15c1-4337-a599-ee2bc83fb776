# GreenSoulAI GetHTML

基于AI Agent的智能HTML获取工具，能够从指定URL获取HTML内容并保存到本地文件系统。

## 🌟 功能特性

- **智能HTML获取**: 使用AI Agent从指定URL获取完整HTML内容
- **自动文件保存**: 将获取的HTML内容保存到本地文件系统
- **灵活配置**: 支持自定义保存目录、文件名和超时时间
- **错误处理**: 完善的错误处理和超时控制
- **事件驱动**: 支持事件总线，可实时监控获取过程
- **命令行界面**: 简单易用的命令行工具

## 🚀 快速开始

### 1. 环境准备

**安装Go语言**（如果尚未安装）：
- 访问 https://golang.org/dl/ 下载并安装Go
- 确保Go已添加到系统PATH中

**设置OpenRouter API密钥**：

```bash
export OPENROUTER_API_KEY="your-openrouter-api-key-here"
```

### 2. 编译程序

```bash
# 在项目根目录下
cd examples/gethtml
go build -o gethtml ./cmd
```

**Windows用户**：
```cmd
cd examples\gethtml
go build -o gethtml.exe .\cmd
```

### 3. 基本使用

```bash
# 获取网页并保存到默认目录
./gethtml -url https://example.com

# 指定保存目录和文件名
./gethtml -url https://example.com -dir ./my_html -file example.html

# 启用详细日志
./gethtml -url https://example.com -verbose

# 设置超时时间
./gethtml -url https://example.com -timeout 30s
```

## 📖 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `-url` | string | 必需 | 要获取HTML的URL地址 |
| `-dir` | string | `./html_output` | 保存HTML文件的目录 |
| `-file` | string | 自动生成 | 保存的文件名 |
| `-timeout` | duration | `60s` | 请求超时时间 |
| `-verbose` | bool | `false` | 启用详细日志输出 |
| `-help` | bool | `false` | 显示帮助信息 |

## 🏗️ 项目结构

```
examples/gethtml/
├── gethtml.go          # 核心功能实现
├── gethtml_test.go     # 单元测试
├── cmd/
│   └── gethtml_main.go # 主程序入口
└── README.md           # 项目文档
```

## 🔧 核心组件

### HTTPFetcher

HTTP获取器，负责从URL获取HTML内容并保存到本地：

```go
type HTTPFetcher struct {
    client  *http.Client
    saveDir string
    logger  logger.Logger
}
```

主要方法：
- `FetchHTML(ctx, url)`: 从指定URL获取HTML内容
- `SaveHTML(ctx, content, filename)`: 保存HTML内容到本地文件

### GetHTMLConfig

获取HTML的配置结构：

```go
type GetHTMLConfig struct {
    URL      string  // 目标URL
    SaveDir  string  // 保存目录
    Filename string  // 文件名
}
```

## 🧪 运行测试

```bash
# 运行所有测试
go test -v

# 运行特定测试
go test -v -run TestHTTPFetcher_FetchHTML

# 运行测试并显示覆盖率
go test -v -cover
```

## 📝 使用示例

### 基本用法

```go
package main

import (
    "context"
    "log"
    
    gethtml "github.com/ynl/greensoulai/examples/gethtml"
)

func main() {
    config := gethtml.GetHTMLConfig{
        URL:      "https://example.com",
        SaveDir:  "./output",
        Filename: "example.html",
    }
    
    ctx := context.Background()
    output, err := gethtml.RunGetHTML(ctx, config)
    if err != nil {
        log.Fatal(err)
    }
    
    log.Printf("Success: %s", output.Raw)
}
```

### 带事件总线的用法

```go
package main

import (
    "context"
    "log"
    
    gethtml "github.com/ynl/greensoulai/examples/gethtml"
    "github.com/ynl/greensoulai/pkg/events"
    "github.com/ynl/greensoulai/pkg/logger"
)

func main() {
    // 创建事件总线
    logger := logger.NewConsoleLogger(logger.LevelInfo)
    eventBus := events.NewEventBus(logger)
    
    config := gethtml.GetHTMLConfig{
        URL:      "https://example.com",
        SaveDir:  "./output",
        Filename: "example.html",
    }
    
    ctx := context.Background()
    output, err := gethtml.RunGetHTMLWithEventBus(ctx, config, eventBus)
    if err != nil {
        log.Fatal(err)
    }
    
    log.Printf("Success: %s", output.Raw)
}
```

## 🔍 文件名生成规则

如果未指定文件名，系统会自动生成：

- 移除URL协议前缀（http://、https://）
- 替换特殊字符为下划线
- 添加时间戳
- 添加.html扩展名

示例：
- `https://example.com` → `example.com_20240813_143022.html`
- `https://api.test.com/v1/data?id=123` → `api.test.com_v1_data_id_123_20240813_143022.html`

## 🛠️ 技术架构

本项目基于GreenSoulAI框架构建，采用以下技术：

- **Agent架构**: 使用BaseAgent作为核心执行单元
- **事件驱动**: 支持事件总线进行状态通知
- **LLM集成**: 集成OpenRouter LLM服务
- **日志系统**: 完整的日志记录和错误追踪
- **测试覆盖**: 全面的单元测试

## 🤝 参考项目

本项目参考了GreenSoulAI Garden项目的架构设计和实现模式，采用了类似的：

- Agent配置和初始化方式
- 事件总线集成模式
- 日志记录规范
- 错误处理策略

## 🔧 故障排除

### Agent没有调用工具的问题

如果看到输出 `任务是否使用工具: []`，说明Agent没有调用工具。可能的原因和解决方案：

1. **LLM模型问题**：
   - 确保使用支持工具调用的模型（如 `anthropic/claude-3.5-sonnet`）
   - 避免使用不支持工具的模型（如 `openai/gpt-5-nano`）

2. **API密钥问题**：
   - 确保 `OPENROUTER_API_KEY` 环境变量已正确设置
   - 确保API密钥有效且有足够的额度

3. **工具配置问题**：
   - 确保工具已正确添加到Agent配置中
   - 确保工具的schema定义正确

4. **任务描述问题**：
   - 确保任务描述明确指示Agent使用工具
   - 使用明确的指令，如"你必须使用fetch_html工具"

### 最新修复

最新版本已修复了以下问题：
- ✅ 更改默认模型为 `anthropic/claude-3.5-sonnet`（支持工具调用）
- ✅ 在Agent配置中直接添加工具
- ✅ 优化任务描述，明确指示使用工具
- ✅ 改进工具参数传递

### 验证工具调用

成功的输出应该显示：
```
任务是否使用工具: [fetch_html save_html]
```

而不是：
```
任务是否使用工具: []
```

## 📄 许可证

本项目遵循与GreenSoulAI主项目相同的许可证。
