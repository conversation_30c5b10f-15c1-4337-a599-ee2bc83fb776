package gethtml

import (
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/ynl/greensoulai/pkg/logger"
)

func TestHTTPFetcher_FetchHTML(t *testing.T) {
	// 创建测试服务器
	testHTML := `<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>This is a test page.</p>
</body>
</html>`

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
		w.Write([]byte(testHTML))
	}))
	defer server.Close()

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "gethtml_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建HTTP获取器
	logger := logger.NewConsoleLogger(logger.LevelError) // 使用Error级别减少测试输出
	fetcher := NewHTTPFetcher(tempDir, logger)

	// 测试获取HTML
	ctx := context.Background()
	content, err := fetcher.FetchHTML(ctx, server.URL)
	if err != nil {
		t.Fatalf("FetchHTML failed: %v", err)
	}

	if content != testHTML {
		t.Errorf("Expected content %q, got %q", testHTML, content)
	}
}

func TestHTTPFetcher_SaveHTML(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "gethtml_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建HTTP获取器
	logger := logger.NewConsoleLogger(logger.LevelError)
	fetcher := NewHTTPFetcher(tempDir, logger)

	// 测试保存HTML
	testContent := "<html><body>Test</body></html>"
	filename := "test.html"

	ctx := context.Background()
	err = fetcher.SaveHTML(ctx, testContent, filename)
	if err != nil {
		t.Fatalf("SaveHTML failed: %v", err)
	}

	// 验证文件是否存在
	filePath := filepath.Join(tempDir, filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Fatalf("File was not created: %s", filePath)
	}

	// 验证文件内容
	savedContent, err := os.ReadFile(filePath)
	if err != nil {
		t.Fatalf("Failed to read saved file: %v", err)
	}

	if string(savedContent) != testContent {
		t.Errorf("Expected content %q, got %q", testContent, string(savedContent))
	}
}

func TestGenerateFilename(t *testing.T) {
	tests := []struct {
		url      string
		expected string
	}{
		{
			url:      "https://example.com",
			expected: "example.com",
		},
		{
			url:      "http://test.com/page?id=123",
			expected: "test.com_page_id_123",
		},
		{
			url:      "https://api.example.com:8080/v1/data",
			expected: "api.example.com_8080_v1_data",
		},
	}

	for _, tt := range tests {
		t.Run(tt.url, func(t *testing.T) {
			result := GenerateFilename(tt.url)
			
			// 检查是否包含预期的基础部分
			if !strings.Contains(result, tt.expected) {
				t.Errorf("Expected filename to contain %q, got %q", tt.expected, result)
			}

			// 检查是否以.html结尾
			if !strings.HasSuffix(result, ".html") {
				t.Errorf("Expected filename to end with .html, got %q", result)
			}

			// 检查是否包含时间戳
			if !strings.Contains(result, "_") {
				t.Errorf("Expected filename to contain timestamp separator, got %q", result)
			}
		})
	}
}

func TestHTTPFetcher_FetchHTML_Timeout(t *testing.T) {
	// 创建一个慢响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second) // 延迟2秒
		w.Write([]byte("slow response"))
	}))
	defer server.Close()

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "gethtml_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建HTTP获取器，设置短超时时间
	logger := logger.NewConsoleLogger(logger.LevelError)
	fetcher := NewHTTPFetcher(tempDir, logger)
	fetcher.client.Timeout = 500 * time.Millisecond // 500ms超时

	// 测试超时情况
	ctx := context.Background()
	_, err = fetcher.FetchHTML(ctx, server.URL)
	if err == nil {
		t.Fatal("Expected timeout error, but got nil")
	}

	if !strings.Contains(err.Error(), "timeout") && !strings.Contains(err.Error(), "context deadline exceeded") {
		t.Errorf("Expected timeout error, got: %v", err)
	}
}

func TestHTTPFetcher_FetchHTML_404(t *testing.T) {
	// 创建返回404的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusNotFound)
		w.Write([]byte("Not Found"))
	}))
	defer server.Close()

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "gethtml_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建HTTP获取器
	logger := logger.NewConsoleLogger(logger.LevelError)
	fetcher := NewHTTPFetcher(tempDir, logger)

	// 测试404错误
	ctx := context.Background()
	_, err = fetcher.FetchHTML(ctx, server.URL)
	if err == nil {
		t.Fatal("Expected 404 error, but got nil")
	}

	if !strings.Contains(err.Error(), "404") {
		t.Errorf("Expected 404 error, got: %v", err)
	}
}
