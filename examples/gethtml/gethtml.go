package gethtml

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/ynl/greensoulai/internal/agent"
	"github.com/ynl/greensoulai/internal/llm"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
)

// HTMLFetcher 定义HTML获取器的接口
type HTMLFetcher interface {
	FetchHTML(ctx context.Context, url string) (string, error)
	SaveHTML(ctx context.Context, content, filename string) error
}

// HTTPFetcher 实现HTMLFetcher接口
type HTTPFetcher struct {
	client  *http.Client
	saveDir string
	logger  logger.Logger
}

// NewHTTPFetcher 创建新的HTTP获取器
func NewHTTPFetcher(saveDir string, logger logger.Logger) *HTTPFetcher {
	return &HTTPFetcher{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		saveDir: saveDir,
		logger:  logger,
	}
}

// FetchHTML 从指定URL获取HTML内容
func (f *HTTPFetcher) FetchHTML(ctx context.Context, url string) (string, error) {
	f.logger.Info("Fetching HTML from URL", logger.Field{Key: "url", Value: url})

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("create request failed: %w", err)
	}

	// 设置User-Agent避免被反爬虫
	req.Header.Set("User-Agent", "GreenSoulAI-GetHTML/1.0")

	resp, err := f.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("read response body failed: %w", err)
	}

	f.logger.Info("Successfully fetched HTML",
		logger.Field{Key: "url", Value: url},
		logger.Field{Key: "size", Value: len(body)},
	)

	return string(body), nil
}

// SaveHTML 保存HTML内容到本地文件
func (f *HTTPFetcher) SaveHTML(ctx context.Context, content, filename string) error {
	// 确保保存目录存在
	if err := os.MkdirAll(f.saveDir, 0755); err != nil {
		return fmt.Errorf("create save directory failed: %w", err)
	}

	// 构建完整文件路径
	fullPath := filepath.Join(f.saveDir, filename)

	f.logger.Info("Saving HTML to file", logger.Field{Key: "path", Value: fullPath})

	file, err := os.Create(fullPath)
	if err != nil {
		return fmt.Errorf("create file failed: %w", err)
	}
	defer file.Close()

	_, err = file.WriteString(content)
	if err != nil {
		return fmt.Errorf("write file failed: %w", err)
	}

	f.logger.Info("Successfully saved HTML file",
		logger.Field{Key: "path", Value: fullPath},
		logger.Field{Key: "size", Value: len(content)},
	)

	return nil
}

// GetHTMLConfig 获取HTML的配置
type GetHTMLConfig struct {
	URL      string
	SaveDir  string
	Filename string
}

// RunGetHTML 运行HTML获取任务
func RunGetHTML(ctx context.Context, config GetHTMLConfig) (*agent.TaskOutput, error) {
	return RunGetHTMLWithEventBus(ctx, config, nil)
}

// 创建HTML获取并保存工具（合并版本）
func NewHTMLFetchAndSaveTool(fetcher HTMLFetcher) agent.Tool {
	tool := agent.NewBaseTool(
		"fetch_and_save_html",
		"从指定URL获取HTML内容并保存到本地文件",
		func(ctx context.Context, args map[string]interface{}) (interface{}, error) {
			fmt.Printf("🚀 [fetch_and_save_html工具] 工具被调用！参数: %+v\n", args)

			url, ok := args["url"].(string)
			if !ok {
				fmt.Printf("❌ [fetch_and_save_html工具] url参数错误: %+v\n", args["url"])
				return nil, fmt.Errorf("url参数是必需的，且必须是字符串类型")
			}

			filename, ok := args["filename"].(string)
			if !ok {
				fmt.Printf("❌ [fetch_and_save_html工具] filename参数错误: %+v\n", args["filename"])
				return nil, fmt.Errorf("filename参数是必需的，且必须是字符串类型")
			}

			fmt.Printf("🔧 [fetch_and_save_html工具] 开始从URL获取HTML: %s\n", url)
			fmt.Printf("📁 [fetch_and_save_html工具] 目标文件: %s\n", filename)

			// 获取当前工作目录
			wd, _ := os.Getwd()
			fmt.Printf("📂 [fetch_and_save_html工具] 当前工作目录: %s\n", wd)

			// 步骤1：获取HTML内容
			content, err := fetcher.FetchHTML(ctx, url)
			if err != nil {
				fmt.Printf("❌ [fetch_and_save_html工具] 获取HTML失败: %v\n", err)
				return nil, fmt.Errorf("获取HTML失败: %w", err)
			}

			fmt.Printf("✅ [fetch_and_save_html工具] 成功获取HTML，大小: %d 字节\n", len(content))

			// 步骤2：保存HTML到文件
			fmt.Printf("🔧 [fetch_and_save_html工具] 开始保存HTML到文件: %s\n", filename)

			err = fetcher.SaveHTML(ctx, content, filename)
			if err != nil {
				fmt.Printf("❌ [fetch_and_save_html工具] 保存HTML失败: %v\n", err)
				return nil, fmt.Errorf("保存HTML失败: %w", err)
			}

			// 验证文件是否真的被创建
			httpFetcher, ok := fetcher.(*HTTPFetcher)
			if !ok {
				fmt.Printf("⚠️  [fetch_and_save_html工具] 无法获取HTTPFetcher实例\n")
				return map[string]interface{}{
					"message":  "HTML获取并保存成功",
					"url":      url,
					"filename": filename,
					"size":     len(content),
				}, nil
			}

			fullPath := filepath.Join(httpFetcher.saveDir, filename)
			if _, err := os.Stat(fullPath); err == nil {
				fmt.Printf("✅ [fetch_and_save_html工具] 文件验证成功: %s\n", fullPath)
			} else {
				fmt.Printf("⚠️  [fetch_and_save_html工具] 文件验证失败: %s, 错误: %v\n", fullPath, err)
			}

			fmt.Printf("✅ [fetch_and_save_html工具] 成功保存HTML文件: %s (大小: %d 字节)\n", filename, len(content))

			return map[string]interface{}{
				"message":  "HTML获取并保存成功",
				"url":      url,
				"filename": filename,
				"size":     len(content),
				"fullPath": fullPath,
			}, nil
		},
	)

	// 设置工具参数模式
	tool.SetSchema(agent.ToolSchema{
		Name:        "fetch_and_save_html",
		Description: "从指定URL获取HTML内容并保存到本地文件",
		Parameters: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"url": map[string]interface{}{
					"type":        "string",
					"description": "要获取HTML内容的URL地址",
				},
				"filename": map[string]interface{}{
					"type":        "string",
					"description": "保存的文件名",
				},
			},
		},
		Required: []string{"url", "filename"},
	})

	return tool
}

// RunGetHTMLWithEventBus 运行HTML获取任务（带事件总线）
func RunGetHTMLWithEventBus(ctx context.Context, config GetHTMLConfig, bus events.EventBus) (*agent.TaskOutput, error) {
	// 创建日志器
	baseLogger := &silentLogger{}

	// 创建LLM提供者
	// 创建 LLM（仅支持 OpenRouter），未配置将报错
	var llmProvider llm.LLM
	if apiKey := os.Getenv("OPENROUTER_API_KEY"); apiKey != "" {
		baseURL := "https://openrouter.ai/api/v1"
		model := os.Getenv("GARDEN_MODEL")
		if model == "" {
			// 使用支持工具调用的模型
			model = "openai/gpt-5-nano"
		}
		llmProvider = llm.NewOpenAILLM(
			model,
			llm.WithAPIKey(apiKey),
			llm.WithBaseURL(baseURL),
			llm.WithTimeout(120*time.Second), // 增加LLM超时到2分钟
			llm.WithMaxRetries(2),            // 减少重试次数避免总时间过长
			llm.WithCustomHeader("HTTP-Referer", "https://github.com/ynl/greensoulai"),
			llm.WithCustomHeader("X-Title", "GreenSoulAI GetHTML"),
		)

	} else {
		return nil, fmt.Errorf("no API key found, set OPENROUTER_API_KEY")
	}

	// 创建HTML获取器，设置更长的HTTP超时
	httpFetcher := &HTTPFetcher{
		client: &http.Client{
			Timeout: 60 * time.Second, // HTTP请求超时设置为1分钟
		},
		saveDir: config.SaveDir,
		logger:  baseLogger,
	}

	// 创建HTML获取Agent
	htmlAgent, err := agent.NewBaseAgent(agent.AgentConfig{
		Role:      "HTMLFetcher",
		Goal:      "从指定URL获取HTML内容并保存到本地文件",
		Backstory: "你是一个专业的网页内容获取助手。你的任务是使用fetch_and_save_html工具来获取HTML内容并保存文件。这个工具会自动完成获取和保存两个步骤，你只需要调用一次即可。",
		LLM:       llmProvider,
		EventBus:  bus,
		Tools:     []agent.Tool{NewHTMLFetchAndSaveTool(httpFetcher)},
	})
	if err != nil {
		return nil, fmt.Errorf("create HTML agent: %w", err)
	}

	// 初始化Agent
	if err := htmlAgent.Initialize(); err != nil {
		return nil, fmt.Errorf("initialize HTML agent: %w", err)
	}

	// 创建任务，使用简化的单一工具
	taskDescription := fmt.Sprintf("请使用fetch_and_save_html工具从URL '%s' 获取HTML内容并保存到文件 '%s'。这个工具会自动完成获取和保存两个步骤。", config.URL, config.Filename)

	task := agent.NewTaskWithOptions(
		taskDescription,
		"成功获取HTML内容并保存到指定文件，返回操作结果和文件信息。",
		agent.WithAssignedAgent(htmlAgent),
		agent.WithTools(NewHTMLFetchAndSaveTool(httpFetcher)),
	)

	// 让Agent执行任务（Agent会自动调用工具）
	output, err := htmlAgent.Execute(ctx, task)
	if err != nil {
		return nil, fmt.Errorf("execute HTML agent task: %w", err)
	}

	return output, nil
}

// GenerateFilename 根据URL生成合适的文件名
func GenerateFilename(url string) string {
	// 移除协议前缀
	filename := strings.TrimPrefix(url, "http://")
	filename = strings.TrimPrefix(filename, "https://")

	// 替换特殊字符
	filename = strings.ReplaceAll(filename, "/", "_")
	filename = strings.ReplaceAll(filename, ":", "_")
	filename = strings.ReplaceAll(filename, "?", "_")
	filename = strings.ReplaceAll(filename, "&", "_")
	filename = strings.ReplaceAll(filename, "=", "_")

	// 添加时间戳和扩展名
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("%s_%s.html", filename, timestamp)
}

type silentLogger struct{}

func (l *silentLogger) Debug(msg string, fields ...logger.Field)  {}
func (l *silentLogger) Info(msg string, fields ...logger.Field)   {}
func (l *silentLogger) Warn(msg string, fields ...logger.Field)   {}
func (l *silentLogger) Error(msg string, fields ...logger.Field)  {}
func (l *silentLogger) Fatal(msg string, fields ...logger.Field)  {}
func (l *silentLogger) With(fields ...logger.Field) logger.Logger { return l }
