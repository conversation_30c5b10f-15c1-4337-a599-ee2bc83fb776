# 如何将agent信息发送给前端？

***使用webSocket*** 实时与前端保持通信

项目架构中包含**事件总线的实现与创建**：

```go
// eventBus 事件总线实现
type eventBus struct {
    handlers map[string][]EventHandler
    mu       sync.RWMutex
    logger   logger.Logger
}

// scopedEventBus 作用域事件总线，用于临时处理器管理
type scopedEventBus struct {
    *eventBus
    originalHandlers map[string][]EventHandler
}

// NewEventBus 创建新的事件总线
func NewEventBus(logger logger.Logger) EventBus {
    return &eventBus{
       handlers: make(map[string][]EventHandler),
       logger:   logger,
    }
}
```

以及事件的订阅：

```go
// Subscribe 订阅事件
func (eb *eventBus) Subscribe(eventType string, handler EventHandler) error {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	if eb.handlers[eventType] == nil {
		eb.handlers[eventType] = make([]EventHandler, 0)
	}
	eb.handlers[eventType] = append(eb.handlers[eventType], handler)

	eb.logger.Info("event handler registered",
		logger.Field{Key: "event_type", Value: eventType},
	)
	return nil
}
```

因此，对于**每个agent的动作**，我都可以将他作为一个事件进行**发布**，对应写一个服务**接收**事件，最后将事件内容通过WebSocket连接发送给前端

首先创建事件总线，作为事件处理中心的支持：

```go
// 创建事件总线
eventBus := events.NewEventBus(&silentLogger{})
```

# 如何实现WebSocket连接？

## Agent执行顺序分析

通过浏览garden.go，可以发现整个生命流程当中，agent会经历以下过程：

- 1. 智能体创建
  2.  创建Crew协调器

```go
// 初始化Agents
for _, ag := range []agent.Agent{rose, sunflower, lavender, lily, tulip} {
	if err := ag.Initialize(); err != nil {
		return nil, fmt.Errorf("initialize agent %s: %w", ag.GetRole(), err)
	}
}

// 创建Crew（顺序流程）协调每个agent的对话顺序、任务处理流程、上下文处理等
c := crew.NewBaseCrew(&crew.CrewConfig{
	Name:    "GardenCrew",
	Process: crew.ProcessSequential,
	Verbose: true,
}, bus, baseLogger)
```

- 3. 添加Agent到Crew协调器中

```go	
// 添加Agents
for _, ag := range []agent.Agent{rose, sunflower, lavender, lily, tulip} {
	if err := c.AddAgent(ag); err != nil {
		return nil, fmt.Errorf("add agent %s: %w", ag.GetRole(), err)
	}
}
```

- 4. 为不同agent创建任务

```go	
// 创建并分配任务（多轮顺序群聊）：
// Round 1: 5个自我介绍
tRose := agent.NewTaskWithOptions(
	"作为玫瑰，请介绍你的生长需求（土壤、光照、浇灌）与与邻居的相处建议。",
	"简要说明要点，并给1-2条与其他花共植建议。",
	agent.WithAssignedAgent(rose),
)
// 三个round以及最终协商
```

- 5. 将编排好的任务加入到Crew协调器中

```go
	// 添加全部任务：5（R1）+5（R2）+5（R3）+2（协商与最终）= 17
	allTasks := make([]agent.Task, 0, 17)
	allTasks = append(allTasks, r1Tasks...)
	allTasks = append(allTasks, r2Tasks...)
	allTasks = append(allTasks, r3Tasks...)
	allTasks = append(allTasks, tNegotiate, tFinal)
	for _, t := range allTasks {
		if err := c.AddTask(t); err != nil {
			return nil, fmt.Errorf("add task: %w", err)
		}
	}

```

- 6. 最终开始执行任务

```go
output, err := c.Kickoff(ctx, inputs)
```

## Crew协调器内部处理分析

最终开始执行任务时执行了Kickoff方法

```go
output, err := c.Kickoff(ctx, inputs)
```

分析源码，提取处两个关键点：

```go
// base_crew
// func Kickoff()
// 发射开始事件
startEvent := NewCrewKickoffStartedEvent(c.id, c.name, executionID, c.process.String())
c.eventBus.Emit(ctx, c, startEvent)
```

```go
// 发射完成事件
completedEvent := NewCrewKickoffCompletedEvent(c.id, c.name, executionID, duration, err == nil)
c.eventBus.Emit(ctx, c, completedEvent)
```

查看源码发现，开始事件和完成事件分别会发送：

```go
Type:      "crew_kickoff_started"
和
Type:      "crew_kickoff_completed"
```

在执行过程中，也同样具有关注点

```go
// func KickOff()
// 执行任务
start := time.Now()
var result *CrewOutput
var err error

switch c.process {
case ProcessSequential:
	result, err = c.runSequentialProcess(ctx, inputs)
case ProcessHierarchical:
	result, err = c.runHierarchicalProcess(ctx, inputs)
default:
	err = fmt.Errorf("unsupported process: %v", c.process)
}
```

相对应的，会发布：

```go
"sequential_process_started" 
和
"sequential_process_completed"
事件
```

在这两个事件之间，包含了

```go
// process.go
// func runSequentialProcess()
// 执行任务
result, err := c.executeTasks(ctx, c.tasks, inputs)
```

而这个方法同样会发布两个事件

```go
"task_execution_started"
和
"task_execution_completed"
```

并且在执行的过程中，会调用

```go
// process.go
// func executeTasks()
    // 执行任务
	start := time.Now()
	output, err := selectedAgent.Execute(ctx, task)
	duration := time.Since(start)
```

其中：

```go
// base_agent.go
// func Execute()
// 发射开始事件
if a.eventBus != nil {
	startEvent := NewAgentExecutionStartedEvent(
		a.id,
		a.role,
		task.GetID(),
		task.GetDescription(),
		executionID,
	)
	a.eventBus.Emit(ctx, a, startEvent)
}
...

// 发射完成事件
if a.eventBus != nil {
	completedEvent := NewAgentExecutionCompletedEvent(
		a.id,
		a.role,
		task.GetID(),
		task.GetDescription(),
		executionID,
		duration,
			err == nil,
			output,
		)
	a.eventBus.Emit(ctx, a, completedEvent)
}

```

对应：

```go
"agent_execution_started" 和 "agent_execution_completed" 事件
```

这样层层分析，可以获得多个事件名称，以及他们代表的含义

