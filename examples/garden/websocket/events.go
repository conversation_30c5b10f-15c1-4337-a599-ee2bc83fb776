package websocket

import (
	"context"
	"log"
	"strings"
	"time"

	"github.com/ynl/greensoulai/internal/agent"
	"github.com/ynl/greensoulai/pkg/events"
)

// AgentConfig 智能体配置信息
var AgentConfigs = map[string]AgentInfo{
	"Rose": {
		Name:   "Rose",
		Role:   "玫瑰",
		Avatar: "/assets/avatars/rose.svg",
		Color:  "#ff6b9d",
	},
	"Sunflower": {
		Name:   "Sunflower",
		Role:   "向日葵",
		Avatar: "/assets/avatars/sunflower.svg",
		Color:  "#ffd93d",
	},
	"Lavender": {
		Name:   "Lavender",
		Role:   "薰衣草",
		Avatar: "/assets/avatars/lavender.svg",
		Color:  "#b19cd9",
	},
	"Lily": {
		Name:   "Lily",
		Role:   "百合",
		Avatar: "/assets/avatars/lily.svg",
		Color:  "#e8f5e8",
	},
	"Tulip": {
		Name:   "Tulip",
		Role:   "郁金香",
		Avatar: "/assets/avatars/tulip.svg",
		Color:  "#ff4757",
	},
}

// setupEventListeners 设置事件监听器
func (h *Hub) setupEventListeners() {
	//// 监听智能体执行开始事件
	//h.eventBus.Subscribe("agent_execution_started", h.handleAgentExecutionStarted)

	// 监听智能体执行完成事件
	h.eventBus.Subscribe("agent_execution_completed", h.handleAgentExecutionCompleted)

	//// 监听任务开始事件
	//h.eventBus.Subscribe("task_started", h.handleTaskStarted)

	// 监听任务完成事件
	//h.eventBus.Subscribe("task_completed", h.handleTaskCompleted)

	log.Println("WebSocket event listeners registered")
}

// handleAgentExecutionStarted 处理智能体执行开始事件
//func (h *Hub) handleAgentExecutionStarted(ctx context.Context, event events.Event) error {
//	if startedEvent, ok := event.(*agent.AgentExecutionStartedEvent); ok {
//		agentInfo := getAgentInfo(startedEvent.Agent)
//
//		message := AgentMessage{
//			Type:      "agent_message",
//			Timestamp: event.GetTimestamp(),
//			Agent:     agentInfo,
//			Message: MessageInfo{
//				Content:         "🤔 正在思考中...",
//				MessageType:     "start",
//				TaskDescription: startedEvent.Task,
//			},
//		}
//
//		h.BroadcastMessage(message)
//		log.Printf("Broadcasted agent execution started: %s", startedEvent.Agent)
//	}
//
//	return nil
//}

// handleAgentExecutionCompleted 处理智能体执行完成事件
func (h *Hub) handleAgentExecutionCompleted(ctx context.Context, event events.Event) error {
	if completedEvent, ok := event.(*agent.AgentExecutionCompletedEvent); ok {
		agentInfo := getAgentInfo(completedEvent.Agent)

		// 获取输出内容
		var content string
		if completedEvent.Output != nil {
			content = cleanAgentOutput(completedEvent.Output.Raw)
		} else {
			content = "💭 思考完成"
		}

		message := AgentMessage{
			Type:      "agent_message",
			Timestamp: event.GetTimestamp(),
			Agent:     agentInfo,
			Message: MessageInfo{
				Content:         content,
				MessageType:     "response",
				TaskDescription: completedEvent.Task,
			},
		}

		h.BroadcastMessage(message)
		log.Printf("Broadcasted agent execution completed: %s", completedEvent.Agent)
	}

	return nil
}

// handleTaskStarted 处理任务开始事件
//func (h *Hub) handleTaskStarted(ctx context.Context, event events.Event) error {
//	payload := event.GetPayload()
//
//	if taskDesc, ok := payload["task_description"].(string); ok {
//		if agentRole, ok := payload["agent_role"].(string); ok {
//			agentInfo := getAgentInfo(agentRole)
//
//			message := AgentMessage{
//				Type:      "task_message",
//				Timestamp: event.GetTimestamp(),
//				Agent:     agentInfo,
//				Message: MessageInfo{
//					Content:         "📋 开始新任务",
//					MessageType:     "task_start",
//					TaskDescription: taskDesc,
//				},
//			}
//
//			h.BroadcastMessage(message)
//		}
//	}
//
//	return nil
//}

// handleTaskCompleted 处理任务完成事件
func (h *Hub) handleTaskCompleted(ctx context.Context, event events.Event) error {
	payload := event.GetPayload()

	if agentRole, ok := payload["agent_role"].(string); ok {
		agentInfo := getAgentInfo(agentRole)

		message := AgentMessage{
			Type:      "task_message",
			Timestamp: event.GetTimestamp(),
			Agent:     agentInfo,
			Message: MessageInfo{
				Content:         "✅ 任务完成",
				MessageType:     "task_complete",
				TaskDescription: "",
			},
		}

		h.BroadcastMessage(message)
	}

	return nil
}

// getAgentInfo 根据智能体名称获取配置信息
func getAgentInfo(agentName string) AgentInfo {
	if config, exists := AgentConfigs[agentName]; exists {
		return config
	}

	// 默认配置
	return AgentInfo{
		Name:   agentName,
		Role:   agentName,
		Avatar: "/assets/avatars/default.svg",
		Color:  "#6c757d",
	}
}

// cleanAgentOutput 清理智能体输出内容
func cleanAgentOutput(output string) string {
	if output == "" {
		return "💭 思考完成"
	}

	// 移除多余的换行和空格
	content := strings.TrimSpace(output)

	// 增加长度限制到 2000 个字符，以支持更长的回复
	if len(content) > 2000 {
		content = content[:2000] + "..."
	}

	return content
}

// SendSystemMessage 发送系统消息
func (h *Hub) SendSystemMessage(content string) {
	message := AgentMessage{
		Type:      "system_message",
		Timestamp: time.Now(),
		Agent: AgentInfo{
			Name:   "System",
			Role:   "系统",
			Avatar: "/assets/avatars/system.svg",
			Color:  "#6c757d",
		},
		Message: MessageInfo{
			Content:     content,
			MessageType: "info",
		},
	}

	h.BroadcastMessage(message)
}

// SendGardenStatus 发送花园状态消息
func (h *Hub) SendGardenStatus(status string) {
	message := AgentMessage{
		Type:      "garden_status",
		Timestamp: time.Now(),
		Agent: AgentInfo{
			Name:   "Garden",
			Role:   "花园",
			Avatar: "/assets/avatars/garden.svg",
			Color:  "#28a745",
		},
		Message: MessageInfo{
			Content:     status,
			MessageType: "status",
		},
	}

	h.BroadcastMessage(message)
}
