package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"os"
	"os/signal"
	"time"

	"github.com/gorilla/websocket"
)

// 测试消息结构
type TestMessage struct {
	Type      string    `json:"type"`
	Timestamp time.Time `json:"timestamp"`
	Agent     AgentInfo `json:"agent"`
	Message   MessageInfo `json:"message"`
}

type AgentInfo struct {
	Name   string `json:"name"`
	Role   string `json:"role"`
	Avatar string `json:"avatar"`
	Color  string `json:"color"`
}

type MessageInfo struct {
	Content         string `json:"content"`
	MessageType     string `json:"messageType"`
	TaskDescription string `json:"taskDescription"`
}

func main() {
	// 检查服务器是否运行
	serverURL := "ws://localhost:8080/ws"
	
	fmt.Println("🧪 GreenSoulAI Garden WebSocket 测试工具")
	fmt.Printf("连接到: %s\n", serverURL)
	
	// 解析 URL
	u, err := url.Parse(serverURL)
	if err != nil {
		log.Fatal("URL 解析错误:", err)
	}
	
	// 连接 WebSocket
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		log.Fatal("WebSocket 连接失败:", err)
		fmt.Println("\n❌ 连接失败！请确保后端服务正在运行：")
		fmt.Println("   cd examples/garden")
		fmt.Println("   OPENROUTER_API_KEY=your-key go run ./cmd")
		return
	}
	defer c.Close()
	
	fmt.Println("✅ WebSocket 连接成功！")
	
	// 设置中断信号处理
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)
	
	// 消息接收 goroutine
	done := make(chan struct{})
	messageCount := 0
	
	go func() {
		defer close(done)
		for {
			_, message, err := c.ReadMessage()
			if err != nil {
				log.Println("读取消息错误:", err)
				return
			}
			
			messageCount++
			
			// 解析消息
			var testMsg TestMessage
			if err := json.Unmarshal(message, &testMsg); err != nil {
				fmt.Printf("📨 收到原始消息 #%d: %s\n", messageCount, string(message))
			} else {
				fmt.Printf("📨 收到消息 #%d [%s]: %s - %s\n", 
					messageCount, 
					testMsg.Type, 
					testMsg.Agent.Name, 
					testMsg.Message.Content)
			}
		}
	}()
	
	// 定期发送 ping
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	fmt.Println("🔄 开始监听消息... (按 Ctrl+C 退出)")
	fmt.Println("💡 提示: 启动 Garden 后端来查看实时消息")
	
	for {
		select {
		case <-done:
			fmt.Println("\n🔌 连接已关闭")
			return
		case <-interrupt:
			fmt.Println("\n🛑 收到中断信号，正在关闭连接...")
			
			// 发送关闭消息
			err := c.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
			if err != nil {
				log.Println("关闭消息发送错误:", err)
				return
			}
			
			select {
			case <-done:
			case <-time.After(time.Second):
			}
			
			fmt.Printf("✅ 测试完成！共接收 %d 条消息\n", messageCount)
			return
		case <-ticker.C:
			// 发送 ping
			if err := c.WriteMessage(websocket.PingMessage, nil); err != nil {
				log.Println("Ping 发送错误:", err)
				return
			}
		}
	}
}
