/**
 * 消息处理模块
 * 负责处理从 WebSocket 接收的消息，解析消息类型并渲染到界面
 */

class MessageHandler {
    constructor() {
        this.messages = [];
        this.messageContainer = null;
        this.messagesList = null;
        this.maxMessages = 1000; // 最大消息数量
        this.isUserScrolling = false; // 用户是否正在手动滚动
        this.scrollTimeout = null; // 滚动检测超时

        // Agent 配置
        this.agentConfigs = {
            'Rose': { name: '<PERSON>', role: '玫瑰', color: '#ff6b9d', avatar: '/assets/avatars/rose.svg' },
            'Sunflower': { name: 'Sunflower', role: '向日葵', color: '#ffd93d', avatar: '/assets/avatars/sunflower.svg' },
            'Lavender': { name: 'Lavender', role: '薰衣草', color: '#b19cd9', avatar: '/assets/avatars/lavender.svg' },
            'Lily': { name: '<PERSON>', role: '百合', color: '#e8f5e8', avatar: '/assets/avatars/lily.svg' },
            'Tulip': { name: '<PERSON><PERSON>', role: '郁金香', color: '#ff4757', avatar: '/assets/avatars/tulip.svg' },
            'System': { name: 'System', role: '系统', color: '#6c757d', avatar: '/assets/avatars/system.svg' },
            'Garden': { name: 'Garden', role: '花园', color: '#28a745', avatar: '/assets/avatars/garden.svg' }
        };

        this.init();
    }
    
    /**
     * 初始化消息处理器
     */
    init() {
        this.messageContainer = document.getElementById('messagesContainer');
        this.messagesList = document.getElementById('messagesList');

        if (!this.messageContainer || !this.messagesList) {
            console.error('Message container elements not found');
            return;
        }

        // 滚动容器应该是 messagesList，因为它有 overflow-y: auto
        this.scrollContainer = this.messagesList;

        // 初始化 Markdown 渲染器
        this.initMarkdownRenderer();

        // 设置滚动监听
        this.setupScrollListener();

        // 清除欢迎消息
        this.clearWelcomeMessage();
    }

    /**
     * 初始化 Markdown 渲染器
     */
    initMarkdownRenderer() {
        if (typeof marked !== 'undefined') {
            // 配置 marked 选项
            marked.setOptions({
                breaks: true,           // 支持换行符转换为 <br>
                gfm: true,             // 启用 GitHub 风格的 Markdown
                sanitize: false,       // 不使用内置的 sanitize（我们会手动处理）
                smartLists: true,      // 智能列表
                smartypants: true,     // 智能标点符号
                headerIds: false,      // 不生成标题 ID
                mangle: false          // 不混淆邮箱地址
            });
        }
    }

    /**
     * 设置滚动监听器
     */
    setupScrollListener() {
        if (!this.scrollContainer) return;

        this.scrollContainer.addEventListener('scroll', () => {
            // 检测用户是否手动滚动
            const { scrollTop, scrollHeight, clientHeight } = this.scrollContainer;
            const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px 容差

            if (!isAtBottom) {
                this.isUserScrolling = true;
                // 清除之前的超时
                if (this.scrollTimeout) {
                    clearTimeout(this.scrollTimeout);
                }
                // 3秒后重置滚动状态
                this.scrollTimeout = setTimeout(() => {
                    this.isUserScrolling = false;
                }, 3000);
            } else {
                this.isUserScrolling = false;
                if (this.scrollTimeout) {
                    clearTimeout(this.scrollTimeout);
                    this.scrollTimeout = null;
                }
            }
        });
    }
    
    /**
     * 清除欢迎消息
     */
    clearWelcomeMessage() {
        const welcomeMessage = this.messagesList.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.opacity = '0';
            setTimeout(() => {
                welcomeMessage.remove();
            }, 300);
        }
    }
    
    /**
     * 处理接收到的消息
     */
    handleMessage(messageData) {
        try {
            // 验证消息格式
            if (!this.validateMessage(messageData)) {
                console.warn('Invalid message format:', messageData);
                return;
            }

            // 添加消息到列表
            this.addMessage(messageData);

            // 限制消息数量
            this.limitMessages();

        } catch (error) {
            console.error('Error handling message:', error);
        }
    }
    
    /**
     * 验证消息格式
     */
    validateMessage(message) {
        return message && 
               message.type && 
               message.timestamp && 
               message.agent && 
               message.message;
    }
    
    /**
     * 添加消息到界面
     */
    addMessage(messageData) {
        const messageElement = this.createMessageElement(messageData);
        this.messagesList.appendChild(messageElement);
        this.messages.push(messageData);

        // 触发进入动画，并智能滚动到底部
        requestAnimationFrame(() => {
            messageElement.classList.add('message-entered');

            // 等待元素完全渲染后再滚动（仅在用户未手动滚动时）
            setTimeout(() => {
                this.smartScrollToBottom();
            }, 50); // 增加小延迟确保 DOM 更新完成
        });
    }
    
    /**
     * 创建消息元素
     */
    createMessageElement(messageData) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${messageData.type}`;
        messageDiv.setAttribute('data-agent', messageData.agent.name);
        messageDiv.setAttribute('data-timestamp', messageData.timestamp);
        
        // 获取 Agent 配置
        const agentConfig = this.getAgentConfig(messageData.agent.name);
        
        // 设置 CSS 变量用于颜色
        messageDiv.style.setProperty('--agent-color', agentConfig.color);
        
        const timestamp = new Date(messageData.timestamp);
        const timeString = timestamp.toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        });
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <img src="${agentConfig.avatar}" alt="${agentConfig.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="avatar-fallback" style="display: none; background: ${agentConfig.color}; color: white; width: 36px; height: 36px; border-radius: 50%; align-items: center; justify-content: center; font-weight: 600;">
                    ${agentConfig.name[0]}
                </div>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <span class="message-author" style="color: ${agentConfig.color};">${agentConfig.name}</span>
                    <span class="message-role">${agentConfig.role}</span>
                    ${this.getMessageTypeIndicator(messageData.message.messageType)}
                    <span class="message-timestamp" title="${timestamp.toLocaleString('zh-CN')}">${timeString}</span>
                </div>
                <div class="message-body">${this.formatMessageContent(messageData.message.content)}</div>
                ${messageData.message.taskDescription ? `<div class="message-task">${messageData.message.taskDescription}</div>` : ''}
            </div>
        `;
        
        return messageDiv;
    }
    
    /**
     * 获取消息类型指示器
     */
    getMessageTypeIndicator(messageType) {
        const indicators = {
            'start': '<span class="message-type-indicator start">🤔 思考中</span>',
            'response': '<span class="message-type-indicator response">💬 回复</span>',
            'task_start': '<span class="message-type-indicator task-start">📋 任务开始</span>',
            'task_complete': '<span class="message-type-indicator task-complete">✅ 任务完成</span>',
            'info': '<span class="message-type-indicator info">ℹ️ 信息</span>',
            'status': '<span class="message-type-indicator status">📊 状态</span>'
        };
        
        return indicators[messageType] || '';
    }
    
    /**
     * 格式化消息内容
     */
    formatMessageContent(content) {
        if (!content) return '';

        try {
            // 使用 marked 库渲染 Markdown
            if (typeof marked !== 'undefined') {
                // 简单的内容清理（防止恶意脚本）
                const cleanContent = this.sanitizeContent(content);
                const renderedHtml = marked.parse(cleanContent);
                // 后处理：清理多余的空白
                return this.postProcessHtml(renderedHtml);
            } else {
                // 回退到原始的 HTML 转义和换行处理
                return this.escapeHtml(content).replace(/\n/g, '<br>');
            }
        } catch (error) {
            console.error('Error rendering markdown:', error);
            // 发生错误时回退到安全的 HTML 转义
            return this.escapeHtml(content).replace(/\n/g, '<br>');
        }
    }

    /**
     * 后处理 HTML，清理多余的空白
     */
    postProcessHtml(html) {
        return html
            // 移除空的段落标签
            .replace(/<p>\s*<\/p>/g, '')
            // 移除连续的换行符
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            // 移除段落间多余的空白
            .replace(/<\/p>\s*<p>/g, '</p><p>')
            // 移除列表项间多余的空白
            .replace(/<\/li>\s*<li>/g, '</li><li>')
            // 移除标题前后多余的空白
            .replace(/<\/h([1-6])>\s*<p>/g, '</h$1><p>')
            .replace(/<\/p>\s*<h([1-6])>/g, '</p><h$1>')
            // 清理首尾空白
            .trim();
    }

    /**
     * 清理内容，防止 XSS 攻击
     */
    sanitizeContent(content) {
        // 移除潜在的危险标签和属性
        return content
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '');
    }

    /**
     * HTML 转义
     */
    escapeHtml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }
    
    /**
     * 获取 Agent 配置
     */
    getAgentConfig(agentName) {
        return this.agentConfigs[agentName] || {
            name: agentName,
            role: agentName,
            color: '#6c757d',
            avatar: '/assets/avatars/default.svg'
        };
    }
    
    /**
     * 限制消息数量
     */
    limitMessages() {
        while (this.messages.length > this.maxMessages) {
            this.messages.shift();
            const firstMessage = this.messagesList.firstElementChild;
            if (firstMessage && !firstMessage.classList.contains('welcome-message')) {
                firstMessage.remove();
            }
        }
    }
    
    /**
     * 智能滚动到底部（仅在用户未手动滚动时）
     */
    smartScrollToBottom(smooth = true) {
        if (!this.isUserScrolling) {
            this.scrollToBottom(smooth);
        }
    }

    /**
     * 强制滚动到底部
     */
    scrollToBottom(smooth = true) {
        if (this.scrollContainer) {
            // 强制重新计算滚动高度
            const scrollHeight = this.scrollContainer.scrollHeight;
            const clientHeight = this.scrollContainer.clientHeight;

            if (scrollHeight > clientHeight) {
                this.scrollContainer.scrollTo({
                    top: scrollHeight,
                    behavior: smooth ? 'smooth' : 'auto'
                });
            }
        }
    }

    /**
     * 检查是否在底部
     */
    isAtBottom() {
        if (!this.scrollContainer) return true;

        const { scrollTop, scrollHeight, clientHeight } = this.scrollContainer;
        return scrollTop + clientHeight >= scrollHeight - 10; // 10px 容差
    }
    
    /**
     * 清空所有消息
     */
    clearMessages() {
        this.messages = [];
        this.messagesList.innerHTML = '';
        
        // 显示空状态
        this.showEmptyState();
    }
    
    /**
     * 显示空状态
     */
    showEmptyState() {
        const emptyState = document.createElement('div');
        emptyState.className = 'empty-state';
        emptyState.innerHTML = `
            <div class="empty-state-icon">💬</div>
            <div class="empty-state-text">暂无消息</div>
            <div class="empty-state-subtext">等待智能体开始对话...</div>
        `;
        this.messagesList.appendChild(emptyState);
    }
    
    /**
     * 高亮特定 Agent 的消息
     */
    highlightAgentMessages(agentName) {
        const messages = this.messagesList.querySelectorAll('.message');
        messages.forEach(message => {
            if (message.getAttribute('data-agent') === agentName) {
                message.classList.add('highlighted');
            } else {
                message.classList.remove('highlighted');
            }
        });
    }
    
    /**
     * 清除消息高亮
     */
    clearHighlight() {
        const messages = this.messagesList.querySelectorAll('.message.highlighted');
        messages.forEach(message => {
            message.classList.remove('highlighted');
        });
    }
    
    /**
     * 获取消息统计
     */
    getMessageStats() {
        const stats = {
            total: this.messages.length,
            byAgent: {},
            byType: {}
        };
        
        this.messages.forEach(message => {
            // 按 Agent 统计
            const agentName = message.agent.name;
            stats.byAgent[agentName] = (stats.byAgent[agentName] || 0) + 1;
            
            // 按类型统计
            const messageType = message.type;
            stats.byType[messageType] = (stats.byType[messageType] || 0) + 1;
        });
        
        return stats;
    }
}

// 导出 MessageHandler 类
window.MessageHandler = MessageHandler;
