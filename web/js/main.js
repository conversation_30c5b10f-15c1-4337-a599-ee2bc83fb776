/**
 * 主应用程序文件
 * 整合所有模块，初始化应用程序
 */

class GardenChatApp {
    constructor() {
        this.wsManager = null;
        this.messageHandler = null;
        this.uiManager = null;
        this.isInitialized = false;
        
        this.init();
    }
    
    /**
     * 初始化应用程序
     */
    init() {
        // 等待 DOM 加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeApp();
            });
        } else {
            this.initializeApp();
        }
    }
    
    /**
     * 初始化应用程序组件
     */
    initializeApp() {
        try {
            console.log('Initializing GreenSoulAI Garden Chat App...');

            // 初始化 UI 管理器
            this.uiManager = new UIManager();
            window.uiManager = this.uiManager;

            // 初始化消息处理器
            this.messageHandler = new MessageHandler();
            window.messageHandler = this.messageHandler;

            // 初始化 WebSocket 管理器（不自动连接）
            this.wsManager = new WebSocketManager();
            window.wsManager = this.wsManager;

            // 设置事件监听器
            this.setupEventListeners();

            // 检测后端服务器并尝试自动连接
            this.checkBackendAndAutoConnect();

            // 启动后端检测
            this.startBackendDetection();

            this.isInitialized = true;
            console.log('App initialized successfully');

        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showErrorMessage('应用程序初始化失败，请刷新页面重试。');
        }
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // WebSocket 连接事件
        this.wsManager.on('connecting', () => {
            console.log('WebSocket connecting...');
            this.uiManager.updateConnectionStatus('connecting');
        });
        
        this.wsManager.on('open', () => {
            console.log('WebSocket connected');
            this.uiManager.updateConnectionStatus('connected');
            this.uiManager.addSystemLog('WebSocket 连接成功');
        });
        
        this.wsManager.on('close', (event) => {
            console.log('WebSocket disconnected:', event);
            this.uiManager.updateConnectionStatus('disconnected');
            this.uiManager.addSystemLog(`WebSocket 连接断开: ${event.reason || '未知原因'}`);
        });
        
        this.wsManager.on('error', (error) => {
            console.error('WebSocket error:', error);
            this.uiManager.addSystemLog('WebSocket 连接错误');
        });
        
        this.wsManager.on('message', (messageData) => {
            console.log('Received message:', messageData);
            this.handleWebSocketMessage(messageData);
        });
        
        this.wsManager.on('maxReconnectAttemptsReached', () => {
            console.log('Max reconnect attempts reached');
            this.uiManager.addSystemLog('连接重试次数已达上限，请手动刷新页面');
            this.showErrorMessage('连接已断开，请刷新页面重新连接。');
        });
        
        // 页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && !this.wsManager.isConnected()) {
                console.log('Page became visible, attempting to reconnect...');
                this.wsManager.reconnect();
            }
        });
        
        // 窗口关闭事件
        window.addEventListener('beforeunload', () => {
            if (this.wsManager) {
                this.wsManager.close();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardShortcuts(event);
        });

        // 延迟初始化滚动状态监听器，确保所有组件都已初始化
        setTimeout(() => {
            this.uiManager.initScrollStateListener();
        }, 100);
    }
    
    /**
     * 处理 WebSocket 消息
     */
    handleWebSocketMessage(messageData) {
        try {
            // 处理消息
            this.messageHandler.handleMessage(messageData);
            
            // 更新 UI
            this.uiManager.handleMessage(messageData);
            
            // 特殊消息类型处理
            this.handleSpecialMessages(messageData);
            
        } catch (error) {
            console.error('Error handling WebSocket message:', error);
        }
    }
    
    /**
     * 处理特殊消息类型
     */
    handleSpecialMessages(messageData) {
        switch (messageData.type) {
            case 'system_message':
                if (messageData.message.content.includes('欢迎')) {
                    this.uiManager.addSystemLog('收到欢迎消息');
                }
                break;
                
            case 'garden_status':
                this.uiManager.addSystemLog(`花园状态: ${messageData.message.content}`);
                break;
                
            case 'connection_info':
                if (messageData.payload && messageData.payload.clientCount !== undefined) {
                    this.uiManager.updateOnlineUsers(messageData.payload.clientCount);
                }
                break;
        }
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + K: 清空消息
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            if (this.messageHandler) {
                this.messageHandler.clearMessages();
                this.uiManager.addSystemLog('消息已清空 (快捷键)');
            }
        }
        
        // Ctrl/Cmd + D: 切换主题
        if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
            event.preventDefault();
            if (this.uiManager) {
                this.uiManager.toggleTheme();
            }
        }
        
        // End: 滚动到底部
        if (event.key === 'End') {
            event.preventDefault();
            if (this.messageHandler) {
                this.messageHandler.scrollToBottom();
            }
        }
        
        // Escape: 清除高亮
        if (event.key === 'Escape') {
            if (this.messageHandler) {
                this.messageHandler.clearHighlight();
            }
        }
    }
    
    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        // 创建错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;
        
        errorDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>⚠️</span>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: auto;">×</button>
            </div>
        `;
        
        document.body.appendChild(errorDiv);
        
        // 5秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }
    
    /**
     * 获取应用程序状态
     */
    getAppStatus() {
        return {
            initialized: this.isInitialized,
            wsConnected: this.wsManager ? this.wsManager.isConnected() : false,
            wsState: this.wsManager ? this.wsManager.getConnectionState() : 'unknown',
            messageCount: this.messageHandler ? this.messageHandler.messages.length : 0,
            theme: this.uiManager ? this.uiManager.theme : 'unknown'
        };
    }
    
    /**
     * 手动重连
     */
    reconnect() {
        if (this.wsManager) {
            this.wsManager.reconnect();
            this.uiManager.addSystemLog('手动重连中...');
        }
    }
    
    /**
     * 检测后端服务器并尝试自动连接
     */
    async checkBackendAndAutoConnect() {
        try {
            // 检测后端健康状态
            const response = await fetch('http://localhost:8080/health', {
                method: 'GET',
                timeout: 3000
            });

            if (response.ok) {
                console.log('Backend server detected, auto-connecting...');
                this.uiManager.addSystemLog('检测到后端服务器，自动连接中...');

                // 延迟一点时间再连接，让用户看到状态
                setTimeout(() => {
                    this.wsManager.manualConnect();
                }, 500);
            }
        } catch (error) {
            console.log('Backend server not detected:', error.message);
            this.uiManager.addSystemLog('未检测到后端服务器，请手动连接');
            this.uiManager.updateConnectionStatus('disconnected');
        }
    }

    /**
     * 定期检测后端服务器
     */
    startBackendDetection() {
        // 每30秒检测一次后端服务器
        setInterval(async () => {
            if (!this.wsManager.isConnected()) {
                try {
                    const response = await fetch('http://localhost:8080/health', {
                        method: 'GET',
                        timeout: 2000
                    });

                    if (response.ok) {
                        this.uiManager.addSystemLog('检测到后端服务器已启动');
                        // 显示连接提示
                        this.showInfoMessage('后端服务器已启动，点击连接按钮连接');
                    }
                } catch (error) {
                    // 静默失败，不显示错误
                }
            }
        }, 30000);
    }

    /**
     * 显示信息消息
     */
    showInfoMessage(message) {
        const infoDiv = document.createElement('div');
        infoDiv.className = 'info-notification';
        infoDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #17a2b8;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        infoDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>ℹ️</span>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: auto;">×</button>
            </div>
        `;

        document.body.appendChild(infoDiv);

        // 5秒后自动移除
        setTimeout(() => {
            if (infoDiv.parentElement) {
                infoDiv.remove();
            }
        }, 5000);
    }

    /**
     * 导出聊天记录
     */
    exportChatHistory() {
        if (!this.messageHandler) return;

        const messages = this.messageHandler.messages;
        const exportData = {
            exportTime: new Date().toISOString(),
            messageCount: messages.length,
            messages: messages
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `garden-chat-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);
        this.uiManager.addSystemLog('聊天记录已导出');
    }
}

// 添加 CSS 动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// 初始化应用程序
const app = new GardenChatApp();
window.gardenChatApp = app;

// 开发者工具
if (typeof window !== 'undefined') {
    window.GardenChatDebug = {
        app: app,
        connect: () => app.wsManager && app.wsManager.manualConnect(),
        disconnect: () => app.wsManager && app.wsManager.manualDisconnect(),
        toggleConnection: () => app.wsManager && app.wsManager.toggleConnection(),
        reconnect: () => app.reconnect(),
        exportChat: () => app.exportChatHistory(),
        getStatus: () => app.getAppStatus(),
        clearMessages: () => app.messageHandler && app.messageHandler.clearMessages(),
        toggleTheme: () => app.uiManager && app.uiManager.toggleTheme(),
        checkBackend: () => app.checkBackendAndAutoConnect()
    };

    console.log('GreenSoulAI Garden Chat App loaded. Use window.GardenChatDebug for debugging.');
    console.log('Available commands: connect(), disconnect(), toggleConnection(), checkBackend()');
}
