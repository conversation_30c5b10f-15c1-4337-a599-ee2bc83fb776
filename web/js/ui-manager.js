/**
 * UI 管理模块
 * 负责管理用户界面状态、Agent 状态显示、系统信息更新等
 */

class UIManager {
    constructor() {
        this.elements = {};
        this.agents = {};
        this.systemStats = {
            onlineUsers: 0,
            totalMessages: 0,
            startTime: Date.now(),
            lastActivity: null
        };
        
        this.theme = localStorage.getItem('theme') || 'light';
        
        this.init();
    }
    
    /**
     * 初始化 UI 管理器
     */
    init() {
        this.initElements();
        this.initAgents();
        this.initEventListeners();
        this.applyTheme();
        this.startStatsUpdater();
    }
    
    /**
     * 初始化 DOM 元素引用
     */
    initElements() {
        this.elements = {
            connectionStatus: document.getElementById('connectionStatus'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            connectBtn: document.getElementById('connectBtn'),
            themeToggle: document.getElementById('themeToggle'),
            agentsList: document.getElementById('agentsList'),
            agentsCount: document.getElementById('agentsCount'),
            clearMessages: document.getElementById('clearMessages'),
            scrollToBottom: document.getElementById('scrollToBottom'),
            systemPanel: document.getElementById('systemPanel'),
            systemPanelToggle: document.getElementById('systemPanelToggle'),
            onlineUsers: document.getElementById('onlineUsers'),
            uptime: document.getElementById('uptime'),
            totalMessages: document.getElementById('totalMessages'),
            lastActivity: document.getElementById('lastActivity'),
            systemLogs: document.getElementById('systemLogs'),
            loadingIndicator: document.getElementById('loadingIndicator')
        };
    }
    
    /**
     * 初始化 Agent 数据
     */
    initAgents() {
        const agentConfigs = {
            'Rose': { name: 'Rose', role: '玫瑰', color: '#ff6b9d', status: 'idle' },
            'Sunflower': { name: 'Sunflower', role: '向日葵', color: '#ffd93d', status: 'idle' },
            'Lavender': { name: 'Lavender', role: '薰衣草', color: '#b19cd9', status: 'idle' },
            'Lily': { name: 'Lily', role: '百合', color: '#e8f5e8', status: 'idle' },
            'Tulip': { name: 'Tulip', role: '郁金香', color: '#ff4757', status: 'idle' }
        };
        
        Object.keys(agentConfigs).forEach(agentName => {
            this.agents[agentName] = {
                ...agentConfigs[agentName],
                lastActivity: null,
                messageCount: 0
            };
        });
        
        this.renderAgents();
    }
    
    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 连接按钮
        if (this.elements.connectBtn) {
            this.elements.connectBtn.addEventListener('click', () => {
                this.toggleConnection();
            });
        }

        // 主题切换
        if (this.elements.themeToggle) {
            this.elements.themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
        
        // 清空消息
        if (this.elements.clearMessages) {
            this.elements.clearMessages.addEventListener('click', () => {
                if (window.messageHandler) {
                    window.messageHandler.clearMessages();
                    this.systemStats.totalMessages = 0;
                    this.updateSystemStats();
                    this.addSystemLog('消息已清空');
                }
            });
        }
        
        // 滚动到底部
        if (this.elements.scrollToBottom) {
            this.elements.scrollToBottom.addEventListener('click', () => {
                if (window.messageHandler) {
                    window.messageHandler.scrollToBottom();
                    // 重置用户滚动状态
                    window.messageHandler.isUserScrolling = false;
                    this.updateScrollButtonState();
                }
            });
        }
        
        // 系统面板切换
        if (this.elements.systemPanelToggle) {
            this.elements.systemPanelToggle.addEventListener('click', () => {
                this.toggleSystemPanel();
            });
        }
    }
    
    /**
     * 渲染 Agent 列表
     */
    renderAgents() {
        if (!this.elements.agentsList) return;
        
        this.elements.agentsList.innerHTML = '';
        
        Object.values(this.agents).forEach(agent => {
            const agentCard = this.createAgentCard(agent);
            this.elements.agentsList.appendChild(agentCard);
        });
        
        // 更新 Agent 数量
        if (this.elements.agentsCount) {
            this.elements.agentsCount.textContent = `${Object.keys(this.agents).length} 个智能体`;
        }
    }
    
    /**
     * 创建 Agent 卡片
     */
    createAgentCard(agent) {
        const card = document.createElement('div');
        card.className = `agent-card agent-${agent.name.toLowerCase()}`;
        card.setAttribute('data-agent', agent.name);
        card.style.setProperty('--agent-color', agent.color);
        
        const lastActivityText = agent.lastActivity 
            ? new Date(agent.lastActivity).toLocaleTimeString('zh-CN', { hour12: false })
            : '暂无活动';
        
        card.innerHTML = `
            <div class="agent-header">
                <div class="agent-avatar">
                    <img src="/assets/avatars/${agent.name.toLowerCase()}.svg" alt="${agent.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="avatar-fallback" style="display: none; background: ${agent.color}; color: white; width: 40px; height: 40px; border-radius: 50%; align-items: center; justify-content: center; font-weight: 600;">
                        ${agent.name[0]}
                    </div>
                </div>
                <div class="agent-info">
                    <div class="agent-name">${agent.name}</div>
                    <div class="agent-role">${agent.role}</div>
                </div>
            </div>
            <div class="agent-status">
                <span class="agent-status-indicator ${agent.status}"></span>
                <span class="agent-status-text">${this.getStatusText(agent.status)}</span>
            </div>
            <div class="agent-last-activity">最后活动: ${lastActivityText}</div>
        `;
        
        // 添加点击事件
        card.addEventListener('click', () => {
            this.highlightAgent(agent.name);
        });
        
        return card;
    }
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusTexts = {
            'idle': '空闲',
            'thinking': '思考中',
            'responding': '回复中'
        };
        return statusTexts[status] || '未知';
    }
    
    /**
     * 更新连接状态
     */
    updateConnectionStatus(state, info = {}) {
        if (!this.elements.statusIndicator || !this.elements.statusText) return;

        this.elements.statusIndicator.className = `status-indicator ${state}`;

        const statusTexts = {
            'connected': '已连接',
            'connecting': '连接中...',
            'disconnected': '未连接'
        };

        this.elements.statusText.textContent = statusTexts[state] || '未知状态';

        // 更新连接按钮
        this.updateConnectButton(state);

        // 隐藏加载指示器
        if (state === 'connected' && this.elements.loadingIndicator) {
            this.elements.loadingIndicator.style.display = 'none';
        }

        // 添加系统日志
        this.addSystemLog(`连接状态: ${statusTexts[state]}`);
    }

    /**
     * 更新连接按钮
     */
    updateConnectButton(state) {
        if (!this.elements.connectBtn) return;

        const connectIcon = this.elements.connectBtn.querySelector('.connect-icon');
        const connectText = this.elements.connectBtn.querySelector('.connect-text');

        // 清除所有状态类
        this.elements.connectBtn.classList.remove('connecting', 'connected', 'disconnected');

        switch (state) {
            case 'connected':
                this.elements.connectBtn.classList.add('connected');
                if (connectIcon) connectIcon.textContent = '🔗';
                if (connectText) connectText.textContent = '断开';
                this.elements.connectBtn.title = '断开 WebSocket 连接';
                break;

            case 'connecting':
                this.elements.connectBtn.classList.add('connecting');
                if (connectIcon) connectIcon.textContent = '⏳';
                if (connectText) connectText.textContent = '连接中';
                this.elements.connectBtn.title = '正在连接...';
                break;

            case 'disconnected':
            default:
                this.elements.connectBtn.classList.add('disconnected');
                if (connectIcon) connectIcon.textContent = '🔌';
                if (connectText) connectText.textContent = '连接';
                this.elements.connectBtn.title = '连接到 WebSocket 服务器';
                break;
        }
    }

    /**
     * 切换连接状态
     */
    toggleConnection() {
        if (window.wsManager) {
            window.wsManager.toggleConnection();
        }
    }
    
    /**
     * 更新 Agent 状态
     */
    updateAgentStatus(agentName, status, lastActivity = null) {
        if (!this.agents[agentName]) return;
        
        this.agents[agentName].status = status;
        if (lastActivity) {
            this.agents[agentName].lastActivity = lastActivity;
        }
        
        // 更新 Agent 卡片
        const agentCard = this.elements.agentsList.querySelector(`[data-agent="${agentName}"]`);
        if (agentCard) {
            const statusIndicator = agentCard.querySelector('.agent-status-indicator');
            const statusText = agentCard.querySelector('.agent-status-text');
            const lastActivityElement = agentCard.querySelector('.agent-last-activity');
            
            if (statusIndicator) {
                statusIndicator.className = `agent-status-indicator ${status}`;
            }
            if (statusText) {
                statusText.textContent = this.getStatusText(status);
            }
            if (lastActivityElement && lastActivity) {
                const timeString = new Date(lastActivity).toLocaleTimeString('zh-CN', { hour12: false });
                lastActivityElement.textContent = `最后活动: ${timeString}`;
            }
        }
    }
    
    /**
     * 处理消息更新
     */
    handleMessage(messageData) {
        // 更新消息统计
        this.systemStats.totalMessages++;
        this.systemStats.lastActivity = new Date(messageData.timestamp);
        
        // 更新 Agent 状态
        const agentName = messageData.agent.name;
        if (this.agents[agentName]) {
            this.agents[agentName].messageCount++;
            
            // 根据消息类型更新状态
            if (messageData.message.messageType === 'start') {
                this.updateAgentStatus(agentName, 'thinking', messageData.timestamp);
            } else if (messageData.message.messageType === 'response') {
                this.updateAgentStatus(agentName, 'responding', messageData.timestamp);
                // 2秒后回到空闲状态
                setTimeout(() => {
                    this.updateAgentStatus(agentName, 'idle');
                }, 2000);
            }
        }
        
        this.updateSystemStats();
    }
    
    /**
     * 高亮 Agent
     */
    highlightAgent(agentName) {
        // 清除之前的高亮
        const allCards = this.elements.agentsList.querySelectorAll('.agent-card');
        allCards.forEach(card => card.classList.remove('active'));
        
        // 高亮选中的 Agent
        const selectedCard = this.elements.agentsList.querySelector(`[data-agent="${agentName}"]`);
        if (selectedCard) {
            selectedCard.classList.add('active');
            
            // 高亮消息
            if (window.messageHandler) {
                window.messageHandler.highlightAgentMessages(agentName);
            }
            
            // 3秒后清除高亮
            setTimeout(() => {
                selectedCard.classList.remove('active');
                if (window.messageHandler) {
                    window.messageHandler.clearHighlight();
                }
            }, 3000);
        }
    }
    
    /**
     * 更新系统统计信息
     */
    updateSystemStats() {
        if (this.elements.totalMessages) {
            this.elements.totalMessages.textContent = this.systemStats.totalMessages;
        }
        
        if (this.elements.lastActivity && this.systemStats.lastActivity) {
            this.elements.lastActivity.textContent = this.systemStats.lastActivity.toLocaleTimeString('zh-CN', { hour12: false });
        }
    }
    
    /**
     * 启动统计更新器
     */
    startStatsUpdater() {
        setInterval(() => {
            this.updateUptime();
        }, 1000);
    }
    
    /**
     * 更新运行时间
     */
    updateUptime() {
        if (!this.elements.uptime) return;
        
        const uptime = Date.now() - this.systemStats.startTime;
        const hours = Math.floor(uptime / (1000 * 60 * 60));
        const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((uptime % (1000 * 60)) / 1000);
        
        this.elements.uptime.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    /**
     * 添加系统日志
     */
    addSystemLog(message) {
        if (!this.elements.systemLogs) return;
        
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        const timestamp = new Date().toLocaleTimeString('zh-CN', { hour12: false });
        logEntry.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span> ${message}
        `;
        
        this.elements.systemLogs.appendChild(logEntry);
        
        // 限制日志数量
        const logs = this.elements.systemLogs.querySelectorAll('.log-entry');
        if (logs.length > 50) {
            logs[0].remove();
        }
        
        // 滚动到底部
        this.elements.systemLogs.scrollTop = this.elements.systemLogs.scrollHeight;
    }
    
    /**
     * 切换主题
     */
    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('theme', this.theme);
    }
    
    /**
     * 应用主题
     */
    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        
        if (this.elements.themeToggle) {
            const icon = this.elements.themeToggle.querySelector('.theme-icon');
            if (icon) {
                icon.textContent = this.theme === 'light' ? '🌙' : '☀️';
            }
        }
    }
    
    /**
     * 切换系统面板
     */
    toggleSystemPanel() {
        if (this.elements.systemPanel) {
            this.elements.systemPanel.classList.toggle('collapsed');
        }
    }
    
    /**
     * 更新在线用户数
     */
    updateOnlineUsers(count) {
        this.systemStats.onlineUsers = count;
        if (this.elements.onlineUsers) {
            this.elements.onlineUsers.textContent = count;
        }
    }

    /**
     * 更新滚动按钮状态
     */
    updateScrollButtonState() {
        if (!this.elements.scrollToBottom || !window.messageHandler) return;

        const isAtBottom = window.messageHandler.isAtBottom();
        const isUserScrolling = window.messageHandler.isUserScrolling;

        if (isAtBottom && !isUserScrolling) {
            this.elements.scrollToBottom.style.opacity = '0.5';
            this.elements.scrollToBottom.title = '已在底部';
        } else {
            this.elements.scrollToBottom.style.opacity = '1';
            this.elements.scrollToBottom.title = '滚动到底部';
        }
    }

    /**
     * 初始化滚动状态监听
     */
    initScrollStateListener() {
        if (window.messageHandler && window.messageHandler.scrollContainer) {
            window.messageHandler.scrollContainer.addEventListener('scroll', () => {
                this.updateScrollButtonState();
            });

            // 初始状态更新
            this.updateScrollButtonState();
        }
    }
}

// 导出 UIManager 类
window.UIManager = UIManager;
