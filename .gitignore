# 编译产物
*.exe
main
knowledge
examples/complete/ai_research_assistant
examples/complete/quick_start
examples/complete/simple_kimi_demo

# 临时文件
*.tmp
*.log

# IDE 文件
.vscode/
.idea/

# macOS
.DS_Store

# 备份文件
*.backup_*
*.backup

# 测试文件
*.test
coverage.out
coverage.html
*.prof

# 数据库文件
*.sqlite
*.sqlite3
*.db

# 测试生成的数据文件
internal/memory/long_term/data/
internal/memory/long_term/test-path
internal/memory/long_term/advanced-path
internal/memory/long_term/thread-safe-collection*
internal/training/*_test.json
internal/training/training_data.json
