# GolangCI-Lint 配置文件

run:
  timeout: 5m
  issues-exit-code: 1
  tests: true
  skip-dirs:
    - vendor
    - crewAI  # 跳过Python参考代码
  skip-files:
    - ".*_test.go"
    - ".*\\.pb\\.go"

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true

linters-settings:
  govet:
    check-shadowing: true
    settings:
      printf:
        funcs:
          - (github.com/your-org/greensoulai/pkg/logger.Logger).Debug
          - (github.com/your-org/greensoulai/pkg/logger.Logger).Info
          - (github.com/your-org/greensoulai/pkg/logger.Logger).Warn
          - (github.com/your-org/greensoulai/pkg/logger.Logger).Error
          - (github.com/your-org/greensoulai/pkg/logger.Logger).Fatal

  golint:
    min-confidence: 0

  gocyclo:
    min-complexity: 15

  maligned:
    suggest-new: true

  dupl:
    threshold: 100

  goconst:
    min-len: 2
    min-occurrences: 2

  depguard:
    list-type: blacklist
    packages:
      - github.com/sirupsen/logrus
    packages-with-error-message:
      - github.com/sirupsen/logrus: "请使用 github.com/your-org/greensoulai/pkg/logger 替代"

  misspell:
    locale: US

  lll:
    line-length: 120

  goimports:
    local-prefixes: github.com/your-org/greensoulai

  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc

  funlen:
    lines: 100
    statements: 50

  gci:
    local-prefixes: github.com/your-org/greensoulai

  godox:
    keywords:
      - NOTE
      - OPTIMIZE
      - HACK

  errorlint:
    errorf: true

linters:
  disable-all: true
  enable:
    - bodyclose
    - deadcode
    - depguard
    - dogsled
    - dupl
    - errcheck
    - errorlint
    - exportloopref
    - funlen
    - gci
    - gochecknoinits
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - godox
    - gofmt
    - goimports
    - golint
    - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - interfacer
    - lll
    - maligned
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - rowserrcheck
    - scopelint
    - staticcheck
    - structcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - varcheck
    - whitespace

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - gomnd
        - funlen
        - gocyclo

    - path: cmd/
      linters:
        - gochecknoinits

    - linters:
        - staticcheck
      text: "SA9003:"

    - linters:
        - lll
      source: "^//go:generate "

  exclude-use-default: false
  max-issues-per-linter: 0
  max-same-issues: 0

severity:
  default-severity: error
